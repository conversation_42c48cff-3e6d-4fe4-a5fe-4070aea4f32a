/**
 * @chia/auth - Authentication package for CHIA Portal Ciudadano Digital
 * 
 * This package provides comprehensive authentication functionality including:
 * - User registration and login
 * - Password reset and recovery
 * - Two-factor authentication
 * - Session management
 * - Role-based access control
 * - Audit logging
 */

// Types
export type {
  UserProfile,
  AuthSession,
  LoginCredentials,
  RegistrationData,
  PasswordResetData,
  TwoFactorSetup,
  TwoFactorVerification,
  AuthError,
  AuthContextType,
  LoginFormData,
  RegistrationFormData,
  PasswordResetFormData,
  UpdatePasswordFormData,
  TwoFactorVerificationFormData
} from './types/auth';

export {
  UserRole,
  AuthStatus,
  TwoFactorStatus,
  EmailSchema,
  PasswordSchema,
  PhoneSchema,
  DocumentNumberSchema,
  LoginSchema,
  RegistrationSchema,
  PasswordResetSchema,
  UpdatePasswordSchema,
  TwoFactorVerificationSchema
} from './types/auth';

export type { Database } from './types/database';

// Services
export { AuthService } from './services/auth-service';

// Context and Hooks
export { AuthProvider, AuthContext } from './context/auth-context';
export {
  useAuth,
  useIsAuthenticated,
  useUser,
  useHasRole,
  useAuthLoading,
  useAuthError
} from './hooks/use-auth';

// Supabase Client
export {
  createSupabaseClient,
  createSupabaseAdminClient,
  getSupabaseClient,
  getSupabaseAdminClient,
  resetSupabaseClients,
  isServer,
  getContextualSupabaseClient
} from './lib/supabase-client';

// Re-export commonly used Supabase types
export type { User, Session } from '@supabase/supabase-js';
