'use client';

import { ReactNode } from 'react';
import MainNavigation from './MainNavigation';
import Footer from './Footer';

interface PageLayoutProps {
  children: ReactNode;
  onSearchClick?: () => void;
  onChatClick?: () => void;
  className?: string;
}

const PageLayout = ({ 
  children, 
  onSearchClick, 
  onChatClick, 
  className = '' 
}: PageLayoutProps) => {
  return (
    <div className={`min-h-screen bg-white flex flex-col ${className}`}>
      {/* Navigation */}
      <MainNavigation
        onSearchClick={onSearchClick}
        onChatClick={onChatClick}
      />

      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default PageLayout;
