import { z } from 'zod';

/**
 * User roles in the CHIA system
 */
export enum UserRole {
  CIUDADANO = 'ciudadano',
  ADMIN_MUNICIPAL = 'admin_municipal',
  ADMIN_SISTEMA = 'admin_sistema',
  OPERADOR = 'operador'
}

/**
 * Authentication status enum
 */
export enum AuthStatus {
  LOADING = 'loading',
  AUTHENTICATED = 'authenticated',
  UNAUTHENTICATED = 'unauthenticated',
  ERROR = 'error'
}

/**
 * Two-factor authentication status
 */
export enum TwoFactorStatus {
  DISABLED = 'disabled',
  ENABLED = 'enabled',
  PENDING_SETUP = 'pending_setup'
}

/**
 * Validation schemas using Zod
 */
export const EmailSchema = z.string().email('Email inválido');
export const PasswordSchema = z.string()
  .min(8, 'La contraseña debe tener al menos 8 caracteres')
  .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
    'La contraseña debe contener al menos una mayúscula, una minúscula, un número y un carácter especial');

export const PhoneSchema = z.string()
  .regex(/^\+57[0-9]{10}$/, 'Número de teléfono colombiano inválido (+57XXXXXXXXXX)');

export const DocumentNumberSchema = z.string()
  .min(6, 'Número de documento debe tener al menos 6 dígitos')
  .max(12, 'Número de documento no puede tener más de 12 dígitos')
  .regex(/^\d+$/, 'Número de documento debe contener solo números');

/**
 * User profile interface
 */
export interface UserProfile {
  id: string;
  email: string;
  role: UserRole;
  firstName: string;
  lastName: string;
  documentType: 'CC' | 'CE' | 'TI' | 'PP';
  documentNumber: string;
  phone?: string;
  address?: string;
  city: string;
  department: string;
  birthDate?: string;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  twoFactorEnabled: boolean;
  twoFactorStatus: TwoFactorStatus;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
  metadata?: Record<string, any>;
}

/**
 * Authentication session interface
 */
export interface AuthSession {
  user: UserProfile;
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
  sessionId: string;
}

/**
 * Login credentials interface
 */
export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
  twoFactorCode?: string;
}

/**
 * Registration data interface
 */
export interface RegistrationData {
  email: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  documentType: 'CC' | 'CE' | 'TI' | 'PP';
  documentNumber: string;
  phone?: string;
  city: string;
  department: string;
  birthDate?: string;
  acceptTerms: boolean;
  acceptPrivacyPolicy: boolean;
}

/**
 * Password reset interface
 */
export interface PasswordResetData {
  email: string;
  newPassword: string;
  confirmPassword: string;
  resetToken: string;
}

/**
 * Two-factor authentication setup interface
 */
export interface TwoFactorSetup {
  secret: string;
  qrCodeUrl: string;
  backupCodes: string[];
}

/**
 * Two-factor authentication verification interface
 */
export interface TwoFactorVerification {
  code: string;
  backupCode?: string;
}

/**
 * Authentication error interface
 */
export interface AuthError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

/**
 * Authentication context interface
 */
export interface AuthContextType {
  status: AuthStatus;
  session: AuthSession | null;
  user: UserProfile | null;
  error: AuthError | null;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegistrationData) => Promise<void>;
  logout: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updatePassword: (data: PasswordResetData) => Promise<void>;
  updateProfile: (data: Partial<UserProfile>) => Promise<UserProfile>;
  refreshSession: () => Promise<void>;
  setupTwoFactor: () => Promise<TwoFactorSetup>;
  verifyTwoFactor: (data: TwoFactorVerification) => Promise<void>;
  disableTwoFactor: (password: string) => Promise<void>;
}

/**
 * Validation schemas for forms
 */
export const LoginSchema = z.object({
  email: EmailSchema,
  password: z.string().min(1, 'Contraseña requerida'),
  rememberMe: z.boolean().optional(),
  twoFactorCode: z.string().optional()
});

export const RegistrationSchema = z.object({
  email: EmailSchema,
  password: PasswordSchema,
  confirmPassword: z.string(),
  firstName: z.string().min(2, 'Nombre debe tener al menos 2 caracteres'),
  lastName: z.string().min(2, 'Apellido debe tener al menos 2 caracteres'),
  documentType: z.enum(['CC', 'CE', 'TI', 'PP']),
  documentNumber: DocumentNumberSchema,
  phone: PhoneSchema.optional(),
  city: z.string().min(2, 'Ciudad requerida'),
  department: z.string().min(2, 'Departamento requerido'),
  birthDate: z.string().optional(),
  acceptTerms: z.boolean().refine(val => val === true, 'Debe aceptar los términos y condiciones'),
  acceptPrivacyPolicy: z.boolean().refine(val => val === true, 'Debe aceptar la política de privacidad')
}).refine(data => data.password === data.confirmPassword, {
  message: 'Las contraseñas no coinciden',
  path: ['confirmPassword']
});

export const PasswordResetSchema = z.object({
  email: EmailSchema
});

export const UpdatePasswordSchema = z.object({
  newPassword: PasswordSchema,
  confirmPassword: z.string(),
  resetToken: z.string()
}).refine(data => data.newPassword === data.confirmPassword, {
  message: 'Las contraseñas no coinciden',
  path: ['confirmPassword']
});

export const TwoFactorVerificationSchema = z.object({
  code: z.string().length(6, 'Código debe tener 6 dígitos').regex(/^\d+$/, 'Código debe contener solo números'),
  backupCode: z.string().optional()
});

/**
 * Type exports for form validation
 */
export type LoginFormData = z.infer<typeof LoginSchema>;
export type RegistrationFormData = z.infer<typeof RegistrationSchema>;
export type PasswordResetFormData = z.infer<typeof PasswordResetSchema>;
export type UpdatePasswordFormData = z.infer<typeof UpdatePasswordSchema>;
export type TwoFactorVerificationFormData = z.infer<typeof TwoFactorVerificationSchema>;
