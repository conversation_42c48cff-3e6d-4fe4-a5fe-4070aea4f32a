import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function GET() {
  try {
    console.log('🔍 Iniciando test de Supabase');
    
    // Verificar variables de entorno
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    
    console.log('Variables de entorno:', {
      url: supabaseUrl ? '✅ Definida' : '❌ No definida',
      key: supabaseKey ? '✅ Definida' : '❌ No definida'
    });
    
    if (!supabaseUrl || !supabaseKey) {
      return NextResponse.json({ 
        error: 'Variables de entorno faltantes',
        details: {
          url: !!supabaseUrl,
          key: !!supabaseKey
        }
      }, { status: 500 });
    }

    // Crear cliente
    const supabase = createClient(supabaseUrl, supabaseKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    });

    console.log('✅ Cliente Supabase creado');

    // Probar conexión básica
    const { data: healthCheck, error: healthError } = await supabase
      .from('dependencias')
      .select('count')
      .limit(1);

    if (healthError) {
      console.error('❌ Error en health check (esquema público):', healthError);
      
      // Intentar con esquema ingestion
      const { data: healthCheck2, error: healthError2 } = await supabase
        .schema('ingestion')
        .from('dependencias')
        .select('count')
        .limit(1);
        
      if (healthError2) {
        console.error('❌ Error en health check (esquema ingestion):', healthError2);
        return NextResponse.json({ 
          error: 'Error de conexión a Supabase',
          publicSchema: healthError.message,
          ingestionSchema: healthError2.message
        }, { status: 500 });
      }
      
      console.log('✅ Esquema ingestion funciona');
      return NextResponse.json({ 
        success: true,
        message: 'Conexión exitosa al esquema ingestion',
        data: healthCheck2
      });
    }
    
    console.log('✅ Esquema público funciona');
    return NextResponse.json({ 
      success: true,
      message: 'Conexión exitosa al esquema público',
      data: healthCheck
    });
    
  } catch (error) {
    console.error('❌ Error general:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor',
      details: error instanceof Error ? error.message : 'Error desconocido'
    }, { status: 500 });
  }
}
