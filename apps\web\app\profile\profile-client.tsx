'use client';

import React, { useState } from 'react';
import { useAuth } from '@chia/auth';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { UserProfileForm } from '@/components/profile/user-profile-form';
import { MainNavigation } from '@/components/layout/MainNavigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar,
  Shield,
  Key,
  Bell,
  ArrowLeft
} from 'lucide-react';
import Link from 'next/link';

/**
 * Profile client component
 * User profile management interface
 */
export function ProfileClient() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');

  if (!user) {
    return null;
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <MainNavigation />
        
        <main className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center space-x-4 mb-4">
              <Link href="/dashboard">
                <Button variant="outline" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Volver al Dashboard
                </Button>
              </Link>
            </div>
            
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Mi Perfil</h1>
            <p className="text-gray-600">
              Gestiona tu información personal y configuración de cuenta
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Profile Summary */}
            <div className="lg:col-span-1">
              <Card>
                <CardHeader className="text-center">
                  <div className="mx-auto w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                    <User className="w-10 h-10 text-primary" />
                  </div>
                  <CardTitle>{user.firstName} {user.lastName}</CardTitle>
                  <CardDescription>{user.email}</CardDescription>
                  <div className="flex justify-center mt-2">
                    <Badge variant={user.emailConfirmed ? 'default' : 'secondary'}>
                      {user.emailConfirmed ? 'Verificado' : 'Sin verificar'}
                    </Badge>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  {/* Contact Info */}
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3 text-sm">
                      <Mail className="h-4 w-4 text-gray-500" />
                      <span>{user.email}</span>
                    </div>
                    
                    {user.phone && (
                      <div className="flex items-center space-x-3 text-sm">
                        <Phone className="h-4 w-4 text-gray-500" />
                        <span>{user.phone}</span>
                      </div>
                    )}
                    
                    {user.city && (
                      <div className="flex items-center space-x-3 text-sm">
                        <MapPin className="h-4 w-4 text-gray-500" />
                        <span>{user.city}, {user.department}</span>
                      </div>
                    )}
                    
                    <div className="flex items-center space-x-3 text-sm">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <span>Miembro desde {new Date(user.createdAt).toLocaleDateString('es-CO')}</span>
                    </div>
                  </div>

                  {/* Role Info */}
                  <div className="pt-4 border-t">
                    <div className="flex items-center space-x-2 text-sm">
                      <Shield className="h-4 w-4 text-gray-500" />
                      <span>Rol: </span>
                      <Badge variant="outline">{user.role}</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Profile Management */}
            <div className="lg:col-span-2">
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="profile">
                    <User className="h-4 w-4 mr-2" />
                    Información Personal
                  </TabsTrigger>
                  <TabsTrigger value="security">
                    <Key className="h-4 w-4 mr-2" />
                    Seguridad
                  </TabsTrigger>
                  <TabsTrigger value="notifications">
                    <Bell className="h-4 w-4 mr-2" />
                    Notificaciones
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="profile" className="mt-6">
                  <UserProfileForm user={user} />
                </TabsContent>

                <TabsContent value="security" className="mt-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Configuración de Seguridad</CardTitle>
                      <CardDescription>
                        Gestiona la seguridad de tu cuenta
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="text-center py-8 text-gray-500">
                        <Key className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>Configuración de seguridad</p>
                        <p className="text-sm">Próximamente: Cambio de contraseña y 2FA</p>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="notifications" className="mt-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Preferencias de Notificaciones</CardTitle>
                      <CardDescription>
                        Configura cómo quieres recibir notificaciones
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="text-center py-8 text-gray-500">
                        <Bell className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>Configuración de notificaciones</p>
                        <p className="text-sm">Próximamente: Preferencias de notificación</p>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </main>
      </div>
    </ProtectedRoute>
  );
}
