import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase';

/**
 * GET /api/opas
 * Obtiene lista de OPAs con filtros y paginación
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const categoria = searchParams.get('categoria');
    const dependencia = searchParams.get('dependencia');
    const subdependencia = searchParams.get('subdependencia');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');

    const supabase = await createServerSupabase();

    let query = supabase
      .from('opas_view')
      .select('*', { count: 'exact' });

    // Aplicar filtros
    if (search) {
      query = query.or(`nombre.ilike.%${search}%,descripcion.ilike.%${search}%,objetivo.ilike.%${search}%`);
    }

    if (categoria) {
      query = query.eq('categoria', categoria);
    }

    if (dependencia) {
      query = query.eq('dependencia_id', dependencia);
    }

    if (subdependencia) {
      query = query.eq('subdependencia_id', subdependencia);
    }

    // Aplicar paginación y ordenamiento
    query = query
      .order('dependencia_nombre')
      .order('subdependencia_nombre')
      .order('nombre')
      .range(offset, offset + limit - 1);

    const { data: opas, error, count } = await query;

    if (error) {
      console.error('Error fetching OPAs:', error);
      return NextResponse.json(
        { error: 'Error al obtener OPAs' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: opas,
      pagination: {
        limit,
        offset,
        total: count || 0,
        hasMore: (count || 0) > offset + limit
      }
    });

  } catch (error) {
    console.error('Unexpected error in OPAs API:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/opas
 * Crea una nueva OPA o ejecuta acciones específicas
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;

    const supabase = await createServerSupabase();

    // Acción para obtener categorías
    if (action === 'get_categories') {
      const { data: categories, error } = await supabase
        .from('opas_view')
        .select('categoria')
        .not('categoria', 'is', null);

      if (error) {
        throw error;
      }

      const uniqueCategories = [...new Set(categories?.map(o => o.categoria))].filter(Boolean);
      
      return NextResponse.json({
        success: true,
        data: uniqueCategories
      });
    }

    // Crear nueva OPA
    if (!action) {
      // Verificar autenticación
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError || !user) {
        return NextResponse.json(
          { error: 'No autorizado' },
          { status: 401 }
        );
      }

      // Verificar permisos
      const { data: hasPermission } = await supabase
        .rpc('check_user_permission', {
          p_user_id: user.id,
          p_action: 'INSERT',
          p_table_name: 'opas'
        });

      if (!hasPermission) {
        return NextResponse.json(
          { error: 'Sin permisos para crear OPAs' },
          { status: 403 }
        );
      }

      const {
        nombre,
        descripcion,
        objetivo,
        alcance,
        normatividad,
        responsable,
        tiempo_respuesta,
        canales_atencion,
        palabras_clave,
        categoria,
        subdependencia_id,
        metadata
      } = body;

      // Validar campos requeridos
      if (!nombre || !subdependencia_id) {
        return NextResponse.json(
          { error: 'Nombre y subdependencia son requeridos' },
          { status: 400 }
        );
      }

      // Crear OPA
      const { data: newOpa, error: createError } = await supabase
        .schema('ingestion')
        .from('opas')
        .insert({
          nombre,
          descripcion,
          objetivo,
          alcance,
          normatividad,
          responsable,
          tiempo_respuesta,
          canales_atencion,
          palabras_clave,
          categoria,
          subdependencia_id,
          metadata,
          activo: true,
          fuente_original: 'portal_admin'
        })
        .select()
        .single();

      if (createError) {
        console.error('Error creating OPA:', createError);
        return NextResponse.json(
          { error: 'Error al crear OPA' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        data: newOpa
      });
    }

    return NextResponse.json(
      { error: 'Acción no válida' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error in OPAs POST:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
