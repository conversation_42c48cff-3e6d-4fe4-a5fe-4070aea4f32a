/**
 * Tipos TypeScript para el sistema de ingesta de datos CHIA
 * Arquitecto: Winston 🏗️
 * Fecha: 2025-01-07
 */

// =====================================================
// TIPOS BASE DE DATOS
// =====================================================

export interface Dependencia {
  id?: string;
  codigo: string;
  nombre: string;
  sigla?: string;
  descripcion?: string;
  activo?: boolean;
  metadata?: Record<string, any>;
  created_at?: string;
  updated_at?: string;
}

export interface Subdependencia {
  id?: string;
  codigo: string;
  nombre: string;
  sigla?: string;
  descripcion?: string;
  dependencia_id: string;
  activo?: boolean;
  metadata?: Record<string, any>;
  created_at?: string;
  updated_at?: string;
}

export interface FAQ {
  id?: string;
  dependencia_id: string;
  subdependencia_id?: string;
  tema: string;
  descripcion?: string;
  pregunta: string;
  respuesta: string;
  palabras_clave?: string[];
  prioridad?: number;
  activo?: boolean;
  vistas?: number;
  utilidad_promedio?: number;
  fuente_original?: string;
  version?: number;
  metadata?: Record<string, any>;
  created_at?: string;
  updated_at?: string;
}

export interface Tramite {
  id?: string;
  nombre: string;
  descripcion?: string;
  formulario?: string;
  tiempo_respuesta?: string;
  tiene_pago?: string;
  costo_detalle?: string;
  url_suit?: string;
  url_govco?: string;
  url_formulario?: string;
  dependencia_id: string;
  subdependencia_id?: string;
  palabras_clave?: string[];
  categoria?: string;
  modalidad?: string[];
  requisitos?: Record<string, any>[];
  documentos_requeridos?: Record<string, any>[];
  activo?: boolean;
  popularidad?: number;
  tiempo_promedio_real?: string;
  satisfaccion_promedio?: number;
  fuente_original?: string;
  version?: number;
  metadata?: Record<string, any>;
  created_at?: string;
  updated_at?: string;
}

export interface OPA {
  id?: string;
  codigo_opa: string;
  descripcion: string;
  procedimiento_detallado?: string;
  dependencia_id: string;
  subdependencia_id?: string;
  categoria?: string;
  tipo_procedimiento?: string;
  nivel_complejidad?: number;
  palabras_clave?: string[];
  tramites_relacionados?: string[];
  faqs_relacionadas?: string[];
  activo?: boolean;
  orden_presentacion?: number;
  fuente_original?: string;
  version?: number;
  metadata?: Record<string, any>;
  created_at?: string;
  updated_at?: string;
}

export interface IngestionLog {
  id?: string;
  proceso: string;
  archivo_origen: string;
  estado: 'iniciado' | 'procesando' | 'completado' | 'error';
  registros_procesados?: number;
  registros_exitosos?: number;
  registros_fallidos?: number;
  errores?: Record<string, any>[];
  tiempo_inicio?: string;
  tiempo_fin?: string;
  duracion?: string;
  metadata?: Record<string, any>;
}

// =====================================================
// TIPOS DE ARCHIVOS JSON FUENTE
// =====================================================

export interface FAQSourceData {
  dependencia: string;
  codigo_dependencia: string;
  subdependencia: string;
  codigo_subdependencia: string;
  temas: {
    tema: string;
    descripcion: string;
    preguntas_frecuentes: {
      pregunta: string;
      respuesta: string;
      palabras_clave: string[];
    }[];
  }[];
}

export interface TramiteSourceData {
  Nombre: string;
  Formulario: string;
  "Tiempo de respuesta": string;
  "¿Tiene pago?": string;
  "Visualización trámite en el SUIT": string;
  "Visualización trámite en GOV.CO": string;
  dependencia: string;
  subdependencia: string;
  codigo_dependencia: string;
  codigo_subdependencia: string;
}

export interface OPASourceData {
  dependencias: {
    [codigo_dependencia: string]: {
      nombre: string;
      sigla: string;
      subdependencias: {
        [codigo_subdependencia: string]: {
          nombre: string;
          sigla: string;
          OPA: {
            codigo_OPA: string;
            OPA: string;
          }[];
        };
      };
    };
  };
}

// =====================================================
// TIPOS DE CONFIGURACIÓN
// =====================================================

export interface IngestionConfig {
  batchSize: number;
  maxRetries: number;
  errorThreshold: number;
  parallelWorkers: number;
  validateData: boolean;
  skipDuplicates: boolean;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  dryRun: boolean;
}

export interface ProcessingResult {
  success: boolean;
  processed: number;
  successful: number;
  failed: number;
  errors: ProcessingError[];
  duration: number;
  metadata?: Record<string, any>;
}

export interface ProcessingError {
  type: 'validation' | 'database' | 'transformation' | 'unknown';
  message: string;
  data?: any;
  stack?: string;
  timestamp: string;
}

// =====================================================
// TIPOS DE VALIDACIÓN
// =====================================================

export interface ValidationRule {
  field: string;
  type: 'required' | 'string' | 'number' | 'email' | 'url' | 'array' | 'object';
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => boolean | string;
}

export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
}

export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

// =====================================================
// TIPOS DE TRANSFORMACIÓN
// =====================================================

export interface TransformationRule {
  sourceField: string;
  targetField: string;
  transform?: (value: any) => any;
  defaultValue?: any;
  required?: boolean;
}

export interface DataMapper {
  rules: TransformationRule[];
  customTransforms?: Record<string, (data: any) => any>;
}

// =====================================================
// TIPOS DE ESTADÍSTICAS
// =====================================================

export interface IngestionStats {
  totalFiles: number;
  processedFiles: number;
  totalRecords: number;
  successfulRecords: number;
  failedRecords: number;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  errorRate: number;
  throughput: number; // registros por segundo
}

export interface TableStats {
  tableName: string;
  recordsInserted: number;
  recordsUpdated: number;
  recordsSkipped: number;
  errors: number;
}

// =====================================================
// TIPOS DE MONITOREO
// =====================================================

export interface HealthCheck {
  status: 'healthy' | 'degraded' | 'unhealthy';
  checks: {
    database: boolean;
    fileSystem: boolean;
    memory: boolean;
    diskSpace: boolean;
  };
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface PerformanceMetrics {
  memoryUsage: NodeJS.MemoryUsage;
  cpuUsage: number;
  processingRate: number;
  queueSize: number;
  activeWorkers: number;
  timestamp: Date;
}

// =====================================================
// TIPOS DE EVENTOS
// =====================================================

export type IngestionEvent = 
  | { type: 'started'; data: { config: IngestionConfig } }
  | { type: 'file_processing'; data: { filename: string; progress: number } }
  | { type: 'batch_completed'; data: { batchNumber: number; results: ProcessingResult } }
  | { type: 'error'; data: { error: ProcessingError } }
  | { type: 'completed'; data: { stats: IngestionStats } }
  | { type: 'progress'; data: { percentage: number; message: string } };

export interface EventHandler {
  (event: IngestionEvent): void | Promise<void>;
}

// =====================================================
// TIPOS UTILITARIOS
// =====================================================

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// =====================================================
// CONSTANTES
// =====================================================

export const DEFAULT_INGESTION_CONFIG: IngestionConfig = {
  batchSize: 100,
  maxRetries: 3,
  errorThreshold: 0.1, // 10% de errores máximo
  parallelWorkers: 4,
  validateData: true,
  skipDuplicates: true,
  logLevel: 'info',
  dryRun: false,
};

export const SUPPORTED_FILE_TYPES = ['json'] as const;
export type SupportedFileType = typeof SUPPORTED_FILE_TYPES[number];

export const INGESTION_TABLES = [
  'dependencias',
  'subdependencias', 
  'faqs',
  'tramites',
  'opas'
] as const;
export type IngestionTable = typeof INGESTION_TABLES[number];
