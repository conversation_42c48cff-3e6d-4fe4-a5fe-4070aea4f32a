'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  DocumentTextIcon,
  CreditCardIcon,
  BuildingOfficeIcon,
  IdentificationIcon,
  MagnifyingGlassIcon,
  ClockIcon,
  StarIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface Tramite {
  id: string;
  nombre: string;
  descripcion: string;
  categoria: string;
  tiempoRespuesta: string;
  tienePago: string;
  costoDetalle?: string;
  modalidad: string[];
  requisitos: string[];
  documentosRequeridos: string[];
  urlSuit?: string;
  urlGovco?: string;
  popularidad: number;
  satisfaccion: number;
  dependencia: {
    id: string;
    codigo: string;
    nombre: string;
    sigla?: string;
  };
  subdependencia?: {
    id: string;
    codigo: string;
    nombre: string;
    sigla?: string;
  };
}

interface ServicesGridProps {
  searchQuery?: string;
  selectedCategory?: string;
}

// Icon mapping for different service categories
const getCategoryIcon = (categoria: string) => {
  const iconMap: { [key: string]: any } = {
    'certificados': DocumentTextIcon,
    'pagos': CreditCardIcon,
    'licencias': BuildingOfficeIcon,
    'registro': IdentificationIcon,
    'consultas': MagnifyingGlassIcon,
    'default': DocumentTextIcon
  };
  
  const key = categoria.toLowerCase();
  return iconMap[key] || iconMap.default;
};

// Get service URL based on category or ID
const getServiceUrl = (tramite: Tramite): string => {
  const categoria = tramite.categoria.toLowerCase();
  
  // Map to existing service routes or create new ones
  const urlMap: { [key: string]: string } = {
    'certificados': '/servicios/certificados',
    'pagos': '/servicios/pagos',
    'licencias': '/servicios/licencias',
    'registro': '/servicios/registro',
    'consultas': '/servicios/consultas'
  };
  
  return urlMap[categoria] || `/servicios/tramite/${tramite.id}`;
};

// Determine if a service is popular based on popularity score
const isPopular = (popularidad: number): boolean => {
  return popularidad >= 7; // Services with popularity 7+ are considered popular
};

export default function ServicesGrid({ searchQuery, selectedCategory }: ServicesGridProps) {
  const [tramites, setTramites] = useState<Tramite[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [categories, setCategories] = useState<string[]>([]);

  useEffect(() => {
    fetchTramites();
    fetchCategories();
  }, [searchQuery, selectedCategory]);

  const fetchTramites = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params = new URLSearchParams();
      params.append('limit', '20');
      
      if (searchQuery) {
        params.append('search', searchQuery);
      }
      
      if (selectedCategory && selectedCategory !== 'Todos') {
        params.append('categoria', selectedCategory);
      }

      const response = await fetch(`/api/tramites?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        setTramites(data.data || []);
      } else {
        throw new Error(data.error || 'Failed to fetch services');
      }
    } catch (err) {
      console.error('Error fetching tramites:', err);
      setError(err instanceof Error ? err.message : 'Error loading services');
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/tramites', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'get_categories' }),
      });
      
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setCategories(['Todos', ...data.data]);
        }
      }
    } catch (err) {
      console.error('Error fetching categories:', err);
    }
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {[...Array(6)].map((_, index) => (
          <div key={index} className="bg-white rounded-xl shadow-md p-6 animate-pulse">
            <div className="flex items-start justify-between mb-4">
              <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
              <div className="w-16 h-6 bg-gray-200 rounded-full"></div>
            </div>
            <div className="w-3/4 h-6 bg-gray-200 rounded mb-2"></div>
            <div className="w-full h-4 bg-gray-200 rounded mb-4"></div>
            <div className="w-1/2 h-4 bg-gray-200 rounded mb-4"></div>
            <div className="space-y-2">
              <div className="w-full h-3 bg-gray-200 rounded"></div>
              <div className="w-3/4 h-3 bg-gray-200 rounded"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <ExclamationTriangleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Error al cargar servicios</h3>
        <p className="text-gray-600 mb-4">{error}</p>
        <button
          onClick={fetchTramites}
          className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
        >
          Reintentar
        </button>
      </div>
    );
  }

  if (tramites.length === 0) {
    return (
      <div className="text-center py-12">
        <MagnifyingGlassIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No se encontraron servicios</h3>
        <p className="text-gray-600">
          {searchQuery 
            ? `No hay servicios que coincidan con "${searchQuery}"`
            : 'No hay servicios disponibles en esta categoría'
          }
        </p>
      </div>
    );
  }

  return (
    <>
      {/* Category Filter */}
      {categories.length > 0 && (
        <div className="mb-8">
          <div className="flex flex-wrap gap-2 justify-center">
            {categories.map((category) => (
              <button
                key={category}
                className={`px-4 py-2 rounded-full text-sm font-medium border transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 ${
                  selectedCategory === category || (!selectedCategory && category === 'Todos')
                    ? 'bg-primary-600 border-primary-600 text-white'
                    : 'bg-white border-gray-300 text-gray-700 hover:bg-primary-50 hover:border-primary-300 hover:text-primary-700'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Services Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {tramites.map((tramite) => {
          const IconComponent = getCategoryIcon(tramite.categoria);
          const serviceUrl = getServiceUrl(tramite);
          const popular = isPopular(tramite.popularidad);
          
          return (
            <Link
              key={tramite.id}
              href={serviceUrl}
              className="group bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-200 hover:border-primary-300"
            >
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="p-3 bg-primary-100 rounded-lg group-hover:bg-primary-200 transition-colors">
                      <IconComponent className="h-6 w-6 text-primary-600" />
                    </div>
                    {popular && (
                      <div className="flex items-center gap-1 px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">
                        <StarIcon className="h-3 w-3" />
                        Popular
                      </div>
                    )}
                  </div>
                </div>
                
                <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors">
                  {tramite.nombre}
                </h3>
                
                <p className="text-gray-600 mb-4 text-sm leading-relaxed line-clamp-3">
                  {tramite.descripcion}
                </p>
                
                <div className="flex items-center gap-4 text-sm text-gray-500 mb-4">
                  <div className="flex items-center gap-1">
                    <ClockIcon className="h-4 w-4" />
                    {tramite.tiempoRespuesta}
                  </div>
                  <span className="px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs">
                    {tramite.categoria}
                  </span>
                </div>
                
                <div className="space-y-1">
                  <div className="flex items-center gap-2 text-xs text-gray-500">
                    <div className="w-1 h-1 bg-primary-400 rounded-full"></div>
                    {tramite.dependencia.nombre}
                  </div>
                  {tramite.modalidad.length > 0 && (
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <div className="w-1 h-1 bg-primary-400 rounded-full"></div>
                      {tramite.modalidad.join(', ')}
                    </div>
                  )}
                  {tramite.tienePago !== 'No especificado' && (
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <div className="w-1 h-1 bg-primary-400 rounded-full"></div>
                      {tramite.tienePago}
                    </div>
                  )}
                </div>
              </div>
              
              <div className="px-6 py-4 bg-gray-50 group-hover:bg-primary-50 transition-colors">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700 group-hover:text-primary-700">
                    Acceder al servicio
                  </span>
                  <svg className="h-4 w-4 text-gray-400 group-hover:text-primary-600 group-hover:translate-x-1 transition-all" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </Link>
          );
        })}
      </div>
    </>
  );
}
