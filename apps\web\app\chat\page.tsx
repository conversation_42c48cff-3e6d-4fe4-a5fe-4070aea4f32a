import { Metadata } from 'next';
import PageLayout from '@/components/layout/PageLayout';
import ChatInterface from '@/components/chat/ChatInterface';

export const metadata: Metadata = {
  title: 'Asistente IA | Portal CHIA',
  description: 'Chatea con nuestro asistente de inteligencia artificial para obtener ayuda con servicios municipales, información y trámites.',
  keywords: 'asistente IA, chat, ayuda, servicios municipales, Chía',
};

export default function ChatPage() {
  return (
    <PageLayout className="bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Asistente IA del Portal CHIA
            </h1>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Obtén ayuda instantánea con servicios municipales, información sobre trámites, 
              horarios de atención y mucho más. Nuestro asistente está disponible 24/7.
            </p>
          </div>
        </div>
      </div>

      {/* Chat Interface */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <ChatInterface />
      </div>

      {/* Help Tips */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
        <div className="bg-white rounded-xl shadow-md p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            💡 Consejos para usar el asistente
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Preguntas frecuentes:</h3>
              <ul className="space-y-1">
                <li>• "¿Cómo solicito un certificado de residencia?"</li>
                <li>• "¿Cuáles son los horarios de atención?"</li>
                <li>• "¿Cómo pago mis impuestos en línea?"</li>
                <li>• "¿Qué documentos necesito para una licencia?"</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Funciones disponibles:</h3>
              <ul className="space-y-1">
                <li>• Información sobre servicios municipales</li>
                <li>• Guía paso a paso para trámites</li>
                <li>• Consulta de requisitos y documentos</li>
                <li>• Contacto con funcionarios especializados</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
