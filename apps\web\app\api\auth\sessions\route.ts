import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

/**
 * GET /api/auth/sessions
 * Get active sessions for the current user
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Usuario no autenticado' },
        { status: 401 }
      );
    }

    // Get user sessions from audit logs (simplified approach)
    // In a production system, you'd want a dedicated sessions table
    const { data: sessionLogs, error: logsError } = await supabase
      .from('audit_logs')
      .select('*')
      .eq('user_id', user.id)
      .eq('action', 'user_login')
      .order('created_at', { ascending: false })
      .limit(10);

    if (logsError) {
      console.error('Error fetching session logs:', logsError);
      return NextResponse.json(
        { error: 'Error al obtener sesiones' },
        { status: 500 }
      );
    }

    // Process session data
    const sessions = sessionLogs?.map((log, index) => {
      const details = log.details || {};
      const userAgent = details.user_agent || 'Unknown Browser';
      const ipAddress = details.ip_address || log.ip_address || 'Unknown IP';
      
      // Parse user agent for device info (simplified)
      const isMobile = /Mobile|Android|iPhone|iPad/i.test(userAgent);
      const isTablet = /iPad|Tablet/i.test(userAgent);
      
      let deviceType = 'desktop';
      if (isMobile && !isTablet) deviceType = 'mobile';
      if (isTablet) deviceType = 'tablet';

      // Extract browser name (simplified)
      let browser = 'Unknown Browser';
      if (userAgent.includes('Chrome')) browser = 'Chrome';
      else if (userAgent.includes('Firefox')) browser = 'Firefox';
      else if (userAgent.includes('Safari')) browser = 'Safari';
      else if (userAgent.includes('Edge')) browser = 'Edge';

      return {
        id: log.id,
        deviceType,
        browser,
        location: details.location || 'Ubicación desconocida',
        lastActive: log.created_at,
        current: index === 0, // Most recent session is considered current
        ipAddress,
        userAgent
      };
    }) || [];

    return NextResponse.json({
      sessions,
      total: sessions.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in sessions endpoint:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
