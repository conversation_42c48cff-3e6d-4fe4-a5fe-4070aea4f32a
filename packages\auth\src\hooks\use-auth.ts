import { useContext } from 'react';
import { AuthContext } from '../context/auth-context';
import type { AuthContextType } from '../types/auth';

/**
 * Custom hook to access authentication context
 * 
 * @returns AuthContextType - Authentication context with user state and methods
 * @throws Error if used outside of AuthProvider
 * 
 * @example
 * ```tsx
 * function LoginComponent() {
 *   const { login, user, status } = useAuth();
 *   
 *   const handleLogin = async (credentials) => {
 *     try {
 *       await login(credentials);
 *     } catch (error) {
 *       console.error('Login failed:', error);
 *     }
 *   };
 *   
 *   if (status === 'loading') {
 *     return <div>Loading...</div>;
 *   }
 *   
 *   return (
 *     <form onSubmit={handleLogin}>
 *       // Login form
 *     </form>
 *   );
 * }
 * ```
 */
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  
  if (!context) {
    throw new Error(
      'useAuth must be used within an AuthProvider. ' +
      'Make sure to wrap your component tree with <AuthProvider>.'
    );
  }
  
  return context;
}

/**
 * Hook to check if user is authenticated
 * 
 * @returns boolean - True if user is authenticated
 * 
 * @example
 * ```tsx
 * function ProtectedComponent() {
 *   const isAuthenticated = useIsAuthenticated();
 *   
 *   if (!isAuthenticated) {
 *     return <LoginPrompt />;
 *   }
 *   
 *   return <ProtectedContent />;
 * }
 * ```
 */
export function useIsAuthenticated(): boolean {
  const { status } = useAuth();
  return status === 'authenticated';
}

/**
 * Hook to get current user profile
 * 
 * @returns UserProfile | null - Current user profile or null if not authenticated
 * 
 * @example
 * ```tsx
 * function UserProfile() {
 *   const user = useUser();
 *   
 *   if (!user) {
 *     return <div>Not logged in</div>;
 *   }
 *   
 *   return (
 *     <div>
 *       <h1>Welcome, {user.firstName}!</h1>
 *       <p>Email: {user.email}</p>
 *     </div>
 *   );
 * }
 * ```
 */
export function useUser() {
  const { user } = useAuth();
  return user;
}

/**
 * Hook to check if user has specific role
 * 
 * @param role - Role to check for
 * @returns boolean - True if user has the specified role
 * 
 * @example
 * ```tsx
 * function AdminPanel() {
 *   const isAdmin = useHasRole('admin_sistema');
 *   
 *   if (!isAdmin) {
 *     return <div>Access denied</div>;
 *   }
 *   
 *   return <AdminContent />;
 * }
 * ```
 */
export function useHasRole(role: string): boolean {
  const { user } = useAuth();
  return user?.role === role;
}

/**
 * Hook to check authentication loading state
 * 
 * @returns boolean - True if authentication is loading
 * 
 * @example
 * ```tsx
 * function App() {
 *   const isLoading = useAuthLoading();
 *   
 *   if (isLoading) {
 *     return <LoadingSpinner />;
 *   }
 *   
 *   return <MainApp />;
 * }
 * ```
 */
export function useAuthLoading(): boolean {
  const { status } = useAuth();
  return status === 'loading';
}

/**
 * Hook to get authentication error
 * 
 * @returns AuthError | null - Current authentication error or null
 * 
 * @example
 * ```tsx
 * function LoginForm() {
 *   const error = useAuthError();
 *   
 *   return (
 *     <form>
 *       {error && (
 *         <div className="error">
 *           {error.message}
 *         </div>
 *       )}
 *       // Form fields
 *     </form>
 *   );
 * }
 * ```
 */
export function useAuthError() {
  const { error } = useAuth();
  return error;
}
