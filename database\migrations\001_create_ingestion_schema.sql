-- =====================================================
-- MIGRACIÓN: Esquema de Ingesta de Datos CHIA
-- Versión: 001
-- Fecha: 2025-01-07
-- Descripción: Creación del esquema completo para ingesta
--              de FAQs, Trámites y OPAs del municipio de Chía
-- =====================================================

-- Crear esquema de ingesta
CREATE SCHEMA IF NOT EXISTS ingestion;

-- Habilitar extensiones necesarias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- =====================================================
-- TABLAS DE ESTRUCTURA ORGANIZACIONAL
-- =====================================================

-- Tabla de dependencias principales
CREATE TABLE ingestion.dependencias (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    codigo VARCHAR(10) UNIQUE NOT NULL,
    nombre TEXT NOT NULL,
    sigla VARCHAR(10),
    descripcion TEXT,
    activo BOOLEAN DEFAULT true,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT dependencias_codigo_format CHECK (codigo ~ '^[0-9]{3}$'),
    CONSTRAINT dependencias_nombre_not_empty CHECK (LENGTH(TRIM(nombre)) > 0)
);

-- Tabla de subdependencias
CREATE TABLE ingestion.subdependencias (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    codigo VARCHAR(10) NOT NULL,
    nombre TEXT NOT NULL,
    sigla VARCHAR(10),
    descripcion TEXT,
    dependencia_id UUID NOT NULL REFERENCES ingestion.dependencias(id) ON DELETE CASCADE,
    activo BOOLEAN DEFAULT true,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT subdependencias_codigo_format CHECK (codigo ~ '^[0-9]{3}$'),
    CONSTRAINT subdependencias_nombre_not_empty CHECK (LENGTH(TRIM(nombre)) > 0),
    CONSTRAINT subdependencias_unique_per_dependencia UNIQUE(codigo, dependencia_id)
);

-- =====================================================
-- TABLA DE FAQs
-- =====================================================

CREATE TABLE ingestion.faqs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    dependencia_id UUID NOT NULL REFERENCES ingestion.dependencias(id) ON DELETE CASCADE,
    subdependencia_id UUID REFERENCES ingestion.subdependencias(id) ON DELETE CASCADE,
    tema TEXT NOT NULL,
    descripcion TEXT,
    pregunta TEXT NOT NULL,
    respuesta TEXT NOT NULL,
    palabras_clave JSONB DEFAULT '[]',
    
    -- Campos para búsqueda
    vector_busqueda tsvector,
    embedding_vector vector(1536), -- Para embeddings de OpenAI
    
    -- Metadatos
    prioridad INTEGER DEFAULT 0,
    activo BOOLEAN DEFAULT true,
    vistas INTEGER DEFAULT 0,
    utilidad_promedio DECIMAL(3,2) DEFAULT 0.0,
    
    -- Auditoría
    fuente_original TEXT DEFAULT 'ingesta_inicial',
    version INTEGER DEFAULT 1,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT faqs_pregunta_not_empty CHECK (LENGTH(TRIM(pregunta)) > 0),
    CONSTRAINT faqs_respuesta_not_empty CHECK (LENGTH(TRIM(respuesta)) > 0),
    CONSTRAINT faqs_utilidad_range CHECK (utilidad_promedio >= 0.0 AND utilidad_promedio <= 5.0),
    CONSTRAINT faqs_prioridad_range CHECK (prioridad >= 0 AND prioridad <= 10)
);

-- =====================================================
-- TABLA DE TRÁMITES
-- =====================================================

CREATE TABLE ingestion.tramites (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    nombre TEXT NOT NULL,
    descripcion TEXT,
    formulario TEXT,
    tiempo_respuesta TEXT,
    tiene_pago TEXT,
    costo_detalle TEXT,
    
    -- URLs oficiales
    url_suit TEXT,
    url_govco TEXT,
    url_formulario TEXT,
    
    -- Relaciones organizacionales
    dependencia_id UUID NOT NULL REFERENCES ingestion.dependencias(id) ON DELETE CASCADE,
    subdependencia_id UUID REFERENCES ingestion.subdependencias(id) ON DELETE CASCADE,
    
    -- Campos de búsqueda
    vector_busqueda tsvector,
    palabras_clave JSONB DEFAULT '[]',
    
    -- Metadatos del trámite
    categoria TEXT,
    modalidad TEXT[], -- ['presencial', 'virtual', 'mixto']
    requisitos JSONB DEFAULT '[]',
    documentos_requeridos JSONB DEFAULT '[]',
    
    -- Estado y estadísticas
    activo BOOLEAN DEFAULT true,
    popularidad INTEGER DEFAULT 0,
    tiempo_promedio_real INTERVAL,
    satisfaccion_promedio DECIMAL(3,2) DEFAULT 0.0,
    
    -- Auditoría
    fuente_original TEXT DEFAULT 'ingesta_inicial',
    version INTEGER DEFAULT 1,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT tramites_nombre_not_empty CHECK (LENGTH(TRIM(nombre)) > 0),
    CONSTRAINT tramites_url_suit_format CHECK (url_suit IS NULL OR url_suit ~ '^https?://'),
    CONSTRAINT tramites_url_govco_format CHECK (url_govco IS NULL OR url_govco ~ '^https?://'),
    CONSTRAINT tramites_satisfaccion_range CHECK (satisfaccion_promedio >= 0.0 AND satisfaccion_promedio <= 5.0)
);

-- =====================================================
-- TABLA DE OPAs (Organigrama de Procedimientos Administrativos)
-- =====================================================

CREATE TABLE ingestion.opas (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    codigo_opa VARCHAR(10) NOT NULL,
    descripcion TEXT NOT NULL,
    procedimiento_detallado TEXT,
    
    -- Relaciones organizacionales
    dependencia_id UUID NOT NULL REFERENCES ingestion.dependencias(id) ON DELETE CASCADE,
    subdependencia_id UUID REFERENCES ingestion.subdependencias(id) ON DELETE CASCADE,
    
    -- Clasificación
    categoria TEXT,
    tipo_procedimiento TEXT,
    nivel_complejidad INTEGER DEFAULT 1, -- 1-5
    
    -- Campos de búsqueda
    vector_busqueda tsvector,
    palabras_clave JSONB DEFAULT '[]',
    
    -- Relaciones con otros elementos
    tramites_relacionados UUID[] DEFAULT '{}',
    faqs_relacionadas UUID[] DEFAULT '{}',
    
    -- Estado
    activo BOOLEAN DEFAULT true,
    orden_presentacion INTEGER DEFAULT 0,
    
    -- Auditoría
    fuente_original TEXT DEFAULT 'ingesta_inicial',
    version INTEGER DEFAULT 1,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT opas_codigo_not_empty CHECK (LENGTH(TRIM(codigo_opa)) > 0),
    CONSTRAINT opas_descripcion_not_empty CHECK (LENGTH(TRIM(descripcion)) > 0),
    CONSTRAINT opas_nivel_complejidad_range CHECK (nivel_complejidad >= 1 AND nivel_complejidad <= 5),
    CONSTRAINT opas_unique_per_subdependencia UNIQUE(codigo_opa, dependencia_id, subdependencia_id)
);

-- =====================================================
-- TABLA DE LOG DE INGESTA
-- =====================================================

CREATE TABLE ingestion.ingestion_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    proceso TEXT NOT NULL,
    archivo_origen TEXT NOT NULL,
    estado TEXT NOT NULL, -- 'iniciado', 'procesando', 'completado', 'error'
    registros_procesados INTEGER DEFAULT 0,
    registros_exitosos INTEGER DEFAULT 0,
    registros_fallidos INTEGER DEFAULT 0,
    errores JSONB DEFAULT '[]',
    tiempo_inicio TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    tiempo_fin TIMESTAMP WITH TIME ZONE,
    duracion INTERVAL,
    metadata JSONB DEFAULT '{}',
    
    -- Constraints
    CONSTRAINT ingestion_logs_estado_valid CHECK (estado IN ('iniciado', 'procesando', 'completado', 'error'))
);

-- =====================================================
-- ÍNDICES PARA OPTIMIZACIÓN DE CONSULTAS
-- =====================================================

-- Índices para dependencias
CREATE INDEX idx_dependencias_codigo ON ingestion.dependencias(codigo);
CREATE INDEX idx_dependencias_activo ON ingestion.dependencias(activo);
CREATE INDEX idx_dependencias_nombre_gin ON ingestion.dependencias USING gin(to_tsvector('spanish', nombre));

-- Índices para subdependencias
CREATE INDEX idx_subdependencias_dependencia ON ingestion.subdependencias(dependencia_id);
CREATE INDEX idx_subdependencias_codigo ON ingestion.subdependencias(codigo);
CREATE INDEX idx_subdependencias_activo ON ingestion.subdependencias(activo);

-- Índices para FAQs
CREATE INDEX idx_faqs_dependencia ON ingestion.faqs(dependencia_id);
CREATE INDEX idx_faqs_subdependencia ON ingestion.faqs(subdependencia_id);
CREATE INDEX idx_faqs_activo ON ingestion.faqs(activo);
CREATE INDEX idx_faqs_prioridad ON ingestion.faqs(prioridad DESC);
CREATE INDEX idx_faqs_vector_busqueda ON ingestion.faqs USING gin(vector_busqueda);
CREATE INDEX idx_faqs_palabras_clave ON ingestion.faqs USING gin(palabras_clave);
CREATE INDEX idx_faqs_tema_gin ON ingestion.faqs USING gin(to_tsvector('spanish', tema));
CREATE INDEX idx_faqs_pregunta_gin ON ingestion.faqs USING gin(to_tsvector('spanish', pregunta));

-- Índices para trámites
CREATE INDEX idx_tramites_dependencia ON ingestion.tramites(dependencia_id);
CREATE INDEX idx_tramites_subdependencia ON ingestion.tramites(subdependencia_id);
CREATE INDEX idx_tramites_activo ON ingestion.tramites(activo);
CREATE INDEX idx_tramites_popularidad ON ingestion.tramites(popularidad DESC);
CREATE INDEX idx_tramites_vector_busqueda ON ingestion.tramites USING gin(vector_busqueda);
CREATE INDEX idx_tramites_palabras_clave ON ingestion.tramites USING gin(palabras_clave);
CREATE INDEX idx_tramites_categoria ON ingestion.tramites(categoria);
CREATE INDEX idx_tramites_modalidad ON ingestion.tramites USING gin(modalidad);

-- Índices para OPAs
CREATE INDEX idx_opas_dependencia ON ingestion.opas(dependencia_id);
CREATE INDEX idx_opas_subdependencia ON ingestion.opas(subdependencia_id);
CREATE INDEX idx_opas_activo ON ingestion.opas(activo);
CREATE INDEX idx_opas_codigo ON ingestion.opas(codigo_opa);
CREATE INDEX idx_opas_vector_busqueda ON ingestion.opas USING gin(vector_busqueda);
CREATE INDEX idx_opas_orden ON ingestion.opas(orden_presentacion);

-- Índices para logs de ingesta
CREATE INDEX idx_ingestion_logs_proceso ON ingestion.ingestion_logs(proceso);
CREATE INDEX idx_ingestion_logs_estado ON ingestion.ingestion_logs(estado);
CREATE INDEX idx_ingestion_logs_tiempo ON ingestion.ingestion_logs(tiempo_inicio DESC);

-- =====================================================
-- FUNCIONES AUXILIARES
-- =====================================================

-- Función para actualizar timestamp de updated_at
CREATE OR REPLACE FUNCTION ingestion.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Función para actualizar vector de búsqueda en FAQs
CREATE OR REPLACE FUNCTION ingestion.update_faqs_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.vector_busqueda := to_tsvector('spanish',
        COALESCE(NEW.tema, '') || ' ' ||
        COALESCE(NEW.pregunta, '') || ' ' ||
        COALESCE(NEW.respuesta, '') || ' ' ||
        COALESCE(array_to_string(
            ARRAY(SELECT jsonb_array_elements_text(NEW.palabras_clave)),
            ' '
        ), '')
    );
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Función para actualizar vector de búsqueda en trámites
CREATE OR REPLACE FUNCTION ingestion.update_tramites_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.vector_busqueda := to_tsvector('spanish',
        COALESCE(NEW.nombre, '') || ' ' ||
        COALESCE(NEW.descripcion, '') || ' ' ||
        COALESCE(NEW.categoria, '') || ' ' ||
        COALESCE(array_to_string(NEW.modalidad, ' '), '') || ' ' ||
        COALESCE(array_to_string(
            ARRAY(SELECT jsonb_array_elements_text(NEW.palabras_clave)),
            ' '
        ), '')
    );
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Función para actualizar vector de búsqueda en OPAs
CREATE OR REPLACE FUNCTION ingestion.update_opas_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.vector_busqueda := to_tsvector('spanish',
        COALESCE(NEW.codigo_opa, '') || ' ' ||
        COALESCE(NEW.descripcion, '') || ' ' ||
        COALESCE(NEW.procedimiento_detallado, '') || ' ' ||
        COALESCE(NEW.categoria, '') || ' ' ||
        COALESCE(array_to_string(
            ARRAY(SELECT jsonb_array_elements_text(NEW.palabras_clave)),
            ' '
        ), '')
    );
    RETURN NEW;
END;
$$ language 'plpgsql';

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Triggers para updated_at
CREATE TRIGGER trigger_dependencias_updated_at
    BEFORE UPDATE ON ingestion.dependencias
    FOR EACH ROW EXECUTE FUNCTION ingestion.update_updated_at_column();

CREATE TRIGGER trigger_subdependencias_updated_at
    BEFORE UPDATE ON ingestion.subdependencias
    FOR EACH ROW EXECUTE FUNCTION ingestion.update_updated_at_column();

CREATE TRIGGER trigger_faqs_updated_at
    BEFORE UPDATE ON ingestion.faqs
    FOR EACH ROW EXECUTE FUNCTION ingestion.update_updated_at_column();

CREATE TRIGGER trigger_tramites_updated_at
    BEFORE UPDATE ON ingestion.tramites
    FOR EACH ROW EXECUTE FUNCTION ingestion.update_updated_at_column();

CREATE TRIGGER trigger_opas_updated_at
    BEFORE UPDATE ON ingestion.opas
    FOR EACH ROW EXECUTE FUNCTION ingestion.update_updated_at_column();

-- Triggers para vectores de búsqueda
CREATE TRIGGER trigger_faqs_search_vector
    BEFORE INSERT OR UPDATE ON ingestion.faqs
    FOR EACH ROW EXECUTE FUNCTION ingestion.update_faqs_search_vector();

CREATE TRIGGER trigger_tramites_search_vector
    BEFORE INSERT OR UPDATE ON ingestion.tramites
    FOR EACH ROW EXECUTE FUNCTION ingestion.update_tramites_search_vector();

CREATE TRIGGER trigger_opas_search_vector
    BEFORE INSERT OR UPDATE ON ingestion.opas
    FOR EACH ROW EXECUTE FUNCTION ingestion.update_opas_search_vector();

-- =====================================================
-- POLÍTICAS RLS (Row Level Security)
-- =====================================================

-- Habilitar RLS en todas las tablas
ALTER TABLE ingestion.dependencias ENABLE ROW LEVEL SECURITY;
ALTER TABLE ingestion.subdependencias ENABLE ROW LEVEL SECURITY;
ALTER TABLE ingestion.faqs ENABLE ROW LEVEL SECURITY;
ALTER TABLE ingestion.tramites ENABLE ROW LEVEL SECURITY;
ALTER TABLE ingestion.opas ENABLE ROW LEVEL SECURITY;
ALTER TABLE ingestion.ingestion_logs ENABLE ROW LEVEL SECURITY;

-- Políticas para lectura pública de datos activos
CREATE POLICY "Lectura pública dependencias activas" ON ingestion.dependencias
    FOR SELECT USING (activo = true);

CREATE POLICY "Lectura pública subdependencias activas" ON ingestion.subdependencias
    FOR SELECT USING (activo = true);

CREATE POLICY "Lectura pública FAQs activas" ON ingestion.faqs
    FOR SELECT USING (activo = true);

CREATE POLICY "Lectura pública trámites activos" ON ingestion.tramites
    FOR SELECT USING (activo = true);

CREATE POLICY "Lectura pública OPAs activos" ON ingestion.opas
    FOR SELECT USING (activo = true);

-- Políticas para administradores (rol admin)
CREATE POLICY "Admin acceso completo dependencias" ON ingestion.dependencias
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Admin acceso completo subdependencias" ON ingestion.subdependencias
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Admin acceso completo FAQs" ON ingestion.faqs
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Admin acceso completo trámites" ON ingestion.tramites
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Admin acceso completo OPAs" ON ingestion.opas
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Admin acceso logs" ON ingestion.ingestion_logs
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- =====================================================
-- FUNCIONES DE UTILIDAD PARA BÚSQUEDA
-- =====================================================

-- Función de búsqueda unificada
CREATE OR REPLACE FUNCTION ingestion.buscar_contenido(
    p_query TEXT,
    p_limite INTEGER DEFAULT 20,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE(
    tipo TEXT,
    id UUID,
    titulo TEXT,
    contenido TEXT,
    dependencia TEXT,
    subdependencia TEXT,
    relevancia REAL
) AS $$
BEGIN
    RETURN QUERY
    WITH resultados AS (
        -- Búsqueda en FAQs
        SELECT
            'faq'::TEXT as tipo,
            f.id,
            f.pregunta as titulo,
            f.respuesta as contenido,
            d.nombre as dependencia,
            COALESCE(s.nombre, '') as subdependencia,
            ts_rank(f.vector_busqueda, plainto_tsquery('spanish', p_query)) as relevancia
        FROM ingestion.faqs f
        JOIN ingestion.dependencias d ON f.dependencia_id = d.id
        LEFT JOIN ingestion.subdependencias s ON f.subdependencia_id = s.id
        WHERE f.activo = true
        AND f.vector_busqueda @@ plainto_tsquery('spanish', p_query)

        UNION ALL

        -- Búsqueda en trámites
        SELECT
            'tramite'::TEXT as tipo,
            t.id,
            t.nombre as titulo,
            COALESCE(t.descripcion, '') as contenido,
            d.nombre as dependencia,
            COALESCE(s.nombre, '') as subdependencia,
            ts_rank(t.vector_busqueda, plainto_tsquery('spanish', p_query)) as relevancia
        FROM ingestion.tramites t
        JOIN ingestion.dependencias d ON t.dependencia_id = d.id
        LEFT JOIN ingestion.subdependencias s ON t.subdependencia_id = s.id
        WHERE t.activo = true
        AND t.vector_busqueda @@ plainto_tsquery('spanish', p_query)

        UNION ALL

        -- Búsqueda en OPAs
        SELECT
            'opa'::TEXT as tipo,
            o.id,
            o.codigo_opa as titulo,
            o.descripcion as contenido,
            d.nombre as dependencia,
            COALESCE(s.nombre, '') as subdependencia,
            ts_rank(o.vector_busqueda, plainto_tsquery('spanish', p_query)) as relevancia
        FROM ingestion.opas o
        JOIN ingestion.dependencias d ON o.dependencia_id = d.id
        LEFT JOIN ingestion.subdependencias s ON o.subdependencia_id = s.id
        WHERE o.activo = true
        AND o.vector_busqueda @@ plainto_tsquery('spanish', p_query)
    )
    SELECT r.tipo, r.id, r.titulo, r.contenido, r.dependencia, r.subdependencia, r.relevancia
    FROM resultados r
    ORDER BY r.relevancia DESC
    LIMIT p_limite OFFSET p_offset;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Función para obtener estadísticas de ingesta
CREATE OR REPLACE FUNCTION ingestion.obtener_estadisticas_ingesta()
RETURNS TABLE(
    tabla TEXT,
    total_registros BIGINT,
    registros_activos BIGINT,
    ultima_actualizacion TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 'dependencias'::TEXT,
           COUNT(*)::BIGINT,
           COUNT(*) FILTER (WHERE activo = true)::BIGINT,
           MAX(updated_at)
    FROM ingestion.dependencias

    UNION ALL

    SELECT 'subdependencias'::TEXT,
           COUNT(*)::BIGINT,
           COUNT(*) FILTER (WHERE activo = true)::BIGINT,
           MAX(updated_at)
    FROM ingestion.subdependencias

    UNION ALL

    SELECT 'faqs'::TEXT,
           COUNT(*)::BIGINT,
           COUNT(*) FILTER (WHERE activo = true)::BIGINT,
           MAX(updated_at)
    FROM ingestion.faqs

    UNION ALL

    SELECT 'tramites'::TEXT,
           COUNT(*)::BIGINT,
           COUNT(*) FILTER (WHERE activo = true)::BIGINT,
           MAX(updated_at)
    FROM ingestion.tramites

    UNION ALL

    SELECT 'opas'::TEXT,
           COUNT(*)::BIGINT,
           COUNT(*) FILTER (WHERE activo = true)::BIGINT,
           MAX(updated_at)
    FROM ingestion.opas;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Función para validar integridad referencial
CREATE OR REPLACE FUNCTION ingestion.validar_integridad_datos()
RETURNS TABLE(
    tabla TEXT,
    problema TEXT,
    cantidad BIGINT
) AS $$
BEGIN
    RETURN QUERY
    -- FAQs sin dependencia válida
    SELECT 'faqs'::TEXT,
           'FAQs sin dependencia válida'::TEXT,
           COUNT(*)::BIGINT
    FROM ingestion.faqs f
    LEFT JOIN ingestion.dependencias d ON f.dependencia_id = d.id
    WHERE d.id IS NULL

    UNION ALL

    -- FAQs con subdependencia inválida
    SELECT 'faqs'::TEXT,
           'FAQs con subdependencia inválida'::TEXT,
           COUNT(*)::BIGINT
    FROM ingestion.faqs f
    LEFT JOIN ingestion.subdependencias s ON f.subdependencia_id = s.id
    WHERE f.subdependencia_id IS NOT NULL AND s.id IS NULL

    UNION ALL

    -- Trámites sin dependencia válida
    SELECT 'tramites'::TEXT,
           'Trámites sin dependencia válida'::TEXT,
           COUNT(*)::BIGINT
    FROM ingestion.tramites t
    LEFT JOIN ingestion.dependencias d ON t.dependencia_id = d.id
    WHERE d.id IS NULL

    UNION ALL

    -- OPAs sin dependencia válida
    SELECT 'opas'::TEXT,
           'OPAs sin dependencia válida'::TEXT,
           COUNT(*)::BIGINT
    FROM ingestion.opas o
    LEFT JOIN ingestion.dependencias d ON o.dependencia_id = d.id
    WHERE d.id IS NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- VISTAS PARA CONSULTAS COMUNES
-- =====================================================

-- Vista consolidada de estructura organizacional
CREATE VIEW ingestion.vista_estructura_organizacional AS
SELECT
    d.id as dependencia_id,
    d.codigo as dependencia_codigo,
    d.nombre as dependencia_nombre,
    d.sigla as dependencia_sigla,
    s.id as subdependencia_id,
    s.codigo as subdependencia_codigo,
    s.nombre as subdependencia_nombre,
    s.sigla as subdependencia_sigla,
    d.activo as dependencia_activa,
    s.activo as subdependencia_activa
FROM ingestion.dependencias d
LEFT JOIN ingestion.subdependencias s ON d.id = s.dependencia_id
WHERE d.activo = true
ORDER BY d.codigo, s.codigo;

-- Vista de FAQs con información organizacional
CREATE VIEW ingestion.vista_faqs_completa AS
SELECT
    f.id,
    f.tema,
    f.pregunta,
    f.respuesta,
    f.palabras_clave,
    f.prioridad,
    f.vistas,
    f.utilidad_promedio,
    d.nombre as dependencia,
    d.codigo as dependencia_codigo,
    s.nombre as subdependencia,
    s.codigo as subdependencia_codigo,
    f.created_at,
    f.updated_at
FROM ingestion.faqs f
JOIN ingestion.dependencias d ON f.dependencia_id = d.id
LEFT JOIN ingestion.subdependencias s ON f.subdependencia_id = s.id
WHERE f.activo = true AND d.activo = true
ORDER BY f.prioridad DESC, f.vistas DESC;

-- =====================================================
-- COMENTARIOS PARA DOCUMENTACIÓN
-- =====================================================

COMMENT ON SCHEMA ingestion IS 'Esquema para la ingesta y gestión de datos del sistema CHIA';

COMMENT ON TABLE ingestion.dependencias IS 'Dependencias principales del municipio de Chía';
COMMENT ON TABLE ingestion.subdependencias IS 'Subdependencias organizacionales';
COMMENT ON TABLE ingestion.faqs IS 'Preguntas frecuentes organizadas por dependencia';
COMMENT ON TABLE ingestion.tramites IS 'Catálogo de trámites municipales';
COMMENT ON TABLE ingestion.opas IS 'Organigrama de Procedimientos Administrativos';
COMMENT ON TABLE ingestion.ingestion_logs IS 'Logs de procesos de ingesta de datos';

COMMENT ON FUNCTION ingestion.buscar_contenido IS 'Función de búsqueda unificada en FAQs, trámites y OPAs';
COMMENT ON FUNCTION ingestion.obtener_estadisticas_ingesta IS 'Obtiene estadísticas generales de los datos ingresados';
COMMENT ON FUNCTION ingestion.validar_integridad_datos IS 'Valida la integridad referencial de los datos';

-- =====================================================
-- GRANTS Y PERMISOS
-- =====================================================

-- Permisos para usuarios autenticados (lectura)
GRANT USAGE ON SCHEMA ingestion TO authenticated;
GRANT SELECT ON ALL TABLES IN SCHEMA ingestion TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA ingestion TO authenticated;

-- Permisos para servicio (lectura/escritura)
GRANT ALL ON SCHEMA ingestion TO service_role;
GRANT ALL ON ALL TABLES IN SCHEMA ingestion TO service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA ingestion TO service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA ingestion TO service_role;

-- =====================================================
-- FINALIZACIÓN
-- =====================================================

-- Insertar log de migración
INSERT INTO ingestion.ingestion_logs (
    proceso,
    archivo_origen,
    estado,
    metadata
) VALUES (
    'migracion_esquema',
    '001_create_ingestion_schema.sql',
    'completado',
    '{"version": "001", "descripcion": "Creación inicial del esquema de ingesta"}'
);

-- Mensaje de confirmación
DO $$
BEGIN
    RAISE NOTICE 'Migración 001_create_ingestion_schema.sql completada exitosamente';
    RAISE NOTICE 'Esquema de ingesta creado con % tablas principales', 6;
    RAISE NOTICE 'Sistema listo para ingesta de datos CHIA';
END $$;
