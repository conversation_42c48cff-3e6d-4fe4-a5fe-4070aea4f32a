{"compilerOptions": {"target": "ES2022", "lib": ["dom", "dom.iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./apps/web/*"], "@/ui": ["./packages/ui/src"], "@/database": ["./packages/database/src"], "@/ai": ["./packages/ai/src"], "@/auth": ["./packages/auth/src"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}