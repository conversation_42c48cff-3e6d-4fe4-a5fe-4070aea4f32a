<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Backend Registration</title>
</head>
<body>
    <h1>Test Backend Registration</h1>
    <button id="testButton">Test Registration Backend</button>
    <div id="results"></div>

    <script>
        document.getElementById('testButton').addEventListener('click', async () => {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>Testing registration backend...</p>';

            try {
                // Test data that matches our form
                const testData = {
                    email: '<EMAIL>',
                    password: 'Password123!',
                    confirmPassword: 'Password123!',
                    firstName: '<PERSON>',
                    lastName: '<PERSON>',
                    documentType: 'CC',
                    documentNumber: '1098765432',
                    phone: '',
                    city: 'Chía',
                    department: 'Cundinamarca',
                    birthDate: '',
                    acceptTerms: true,
                    acceptPrivacyPolicy: true
                };

                console.log('Testing with data:', testData);

                // Make a direct API call to test our backend
                const response = await fetch('/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });

                const result = await response.json();
                
                if (response.ok) {
                    resultsDiv.innerHTML = `
                        <h2>✅ Registration Successful!</h2>
                        <p>Backend registration works correctly.</p>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                    console.log('Registration successful:', result);
                } else {
                    resultsDiv.innerHTML = `
                        <h2>❌ Registration Failed</h2>
                        <p>Status: ${response.status}</p>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                    console.error('Registration failed:', result);
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <h2>❌ Error Testing Backend</h2>
                    <p>Error: ${error.message}</p>
                    <pre>${error.stack}</pre>
                `;
                console.error('Error testing backend:', error);
            }
        });
    </script>
</body>
</html>
