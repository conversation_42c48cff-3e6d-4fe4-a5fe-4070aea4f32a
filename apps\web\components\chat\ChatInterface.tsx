'use client';

import { useState, useRef, useEffect } from 'react';
import { 
  PaperAirplaneIcon, 
  UserIcon, 
  ComputerDesktopIcon,
  ClockIcon,
  DocumentTextIcon,
  CreditCardIcon,
  PhoneIcon
} from '@heroicons/react/24/outline';

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'assistant';
  timestamp: Date;
  type?: 'text' | 'quick-action';
}

const quickActions = [
  { id: 'certificados', label: 'Solicitar Certificado', icon: DocumentTextIcon },
  { id: 'pagos', label: 'Pagar Impuestos', icon: CreditCardIcon },
  { id: 'horarios', label: 'Horarios de Atención', icon: ClockIcon },
  { id: 'contacto', label: 'Contactar Funcionario', icon: PhoneIcon },
];

const mockResponses: Record<string, string> = {
  'certificados': 'Para solicitar un certificado de residencia, necesitas:\n\n1. Documento de identidad\n2. Recibo de servicios públicos (no mayor a 3 meses)\n3. Formulario de solicitud\n\n¿Te gustaría que te dirija al formulario en línea?',
  'pagos': 'Puedes pagar tus impuestos municipales de las siguientes formas:\n\n• En línea con tarjeta de crédito/débito\n• PSE (Pagos Seguros en Línea)\n• En bancos autorizados\n• En la oficina municipal\n\n¿Qué tipo de impuesto deseas pagar?',
  'horarios': 'Nuestros horarios de atención son:\n\n🕐 Lunes a Viernes: 8:00 AM - 5:00 PM\n🕐 Sábados: 8:00 AM - 12:00 PM\n❌ Domingos y festivos: Cerrado\n\n📞 Línea de atención: 24/7\n💬 Chat en línea: 24/7',
  'contacto': 'Puedes contactar con nuestros funcionarios:\n\n📧 Email: <EMAIL>\n📞 Teléfono: (*************\n📱 WhatsApp: +57 ************\n\n¿Con qué área específica necesitas comunicarte?',
  'default': 'Hola! Soy el asistente virtual del Portal CHIA. Estoy aquí para ayudarte con información sobre servicios municipales, trámites y consultas generales. ¿En qué puedo ayudarte hoy?'
};

export default function ChatInterface() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: mockResponses.default,
      sender: 'assistant',
      timestamp: new Date(),
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content,
      sender: 'user',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);

    // Simulate AI response delay
    setTimeout(() => {
      const response = getAIResponse(content);
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: response,
        sender: 'assistant',
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, assistantMessage]);
      setIsTyping(false);
    }, 1500);
  };

  const getAIResponse = (input: string): string => {
    const lowerInput = input.toLowerCase();
    
    if (lowerInput.includes('certificado') || lowerInput.includes('residencia')) {
      return mockResponses.certificados;
    } else if (lowerInput.includes('pago') || lowerInput.includes('impuesto')) {
      return mockResponses.pagos;
    } else if (lowerInput.includes('horario') || lowerInput.includes('atención')) {
      return mockResponses.horarios;
    } else if (lowerInput.includes('contacto') || lowerInput.includes('teléfono')) {
      return mockResponses.contacto;
    } else {
      return `Entiendo que preguntas sobre "${input}". Te puedo ayudar con información sobre servicios municipales, trámites, pagos, certificados y más. ¿Podrías ser más específico sobre lo que necesitas?`;
    }
  };

  const handleQuickAction = (actionId: string) => {
    const action = quickActions.find(a => a.id === actionId);
    if (action) {
      handleSendMessage(action.label);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSendMessage(inputValue);
  };

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden h-[600px] flex flex-col">
      {/* Chat Header */}
      <div className="bg-primary-600 text-white p-4">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-primary-500 rounded-full flex items-center justify-center">
            <ComputerDesktopIcon className="h-6 w-6" />
          </div>
          <div>
            <h3 className="font-semibold">Asistente IA CHIA</h3>
            <p className="text-primary-100 text-sm">En línea • Responde en segundos</p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="p-4 border-b border-gray-200">
        <p className="text-sm text-gray-600 mb-3">Acciones rápidas:</p>
        <div className="grid grid-cols-2 gap-2">
          {quickActions.map((action) => (
            <button
              key={action.id}
              onClick={() => handleQuickAction(action.id)}
              className="flex items-center gap-2 p-2 text-sm bg-gray-50 hover:bg-primary-50 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <action.icon className="h-4 w-4 text-primary-600" />
              {action.label}
            </button>
          ))}
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div className={`flex gap-3 max-w-[80%] ${message.sender === 'user' ? 'flex-row-reverse' : ''}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                message.sender === 'user' 
                  ? 'bg-primary-600 text-white' 
                  : 'bg-gray-200 text-gray-600'
              }`}>
                {message.sender === 'user' ? (
                  <UserIcon className="h-4 w-4" />
                ) : (
                  <ComputerDesktopIcon className="h-4 w-4" />
                )}
              </div>
              <div className={`rounded-lg p-3 ${
                message.sender === 'user'
                  ? 'bg-primary-600 text-white'
                  : 'bg-gray-100 text-gray-900'
              }`}>
                <p className="text-sm whitespace-pre-line">{message.content}</p>
                <p className={`text-xs mt-1 ${
                  message.sender === 'user' ? 'text-primary-100' : 'text-gray-500'
                }`}>
                  {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </p>
              </div>
            </div>
          </div>
        ))}
        
        {isTyping && (
          <div className="flex justify-start">
            <div className="flex gap-3 max-w-[80%]">
              <div className="w-8 h-8 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
                <ComputerDesktopIcon className="h-4 w-4" />
              </div>
              <div className="bg-gray-100 rounded-lg p-3">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              </div>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t border-gray-200">
        <form onSubmit={handleSubmit} className="flex gap-2">
          <input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder="Escribe tu pregunta..."
            className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            disabled={isTyping}
          />
          <button
            type="submit"
            disabled={!inputValue.trim() || isTyping}
            className="px-4 py-2 bg-primary-600 hover:bg-primary-700 disabled:bg-gray-300 text-white rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
          >
            <PaperAirplaneIcon className="h-5 w-5" />
          </button>
        </form>
      </div>
    </div>
  );
}
