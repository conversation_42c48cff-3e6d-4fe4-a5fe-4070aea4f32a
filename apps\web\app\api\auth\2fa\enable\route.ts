import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

/**
 * POST /api/auth/2fa/enable
 * Enable 2FA for the user and store secret and backup codes
 */
export async function POST(request: NextRequest) {
  try {
    const { secret, backupCodes } = await request.json();

    if (!secret || !backupCodes) {
      return NextResponse.json(
        { error: 'Secreto y códigos de respaldo requeridos' },
        { status: 400 }
      );
    }

    const supabase = createRouteHandlerClient({ cookies });

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Usuario no autenticado' },
        { status: 401 }
      );
    }

    // Hash backup codes for storage
    const hashedBackupCodes = await Promise.all(
      backupCodes.map(async (code: string) => {
        const encoder = new TextEncoder();
        const data = encoder.encode(code);
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        return Array.from(new Uint8Array(hashBuffer))
          .map(b => b.toString(16).padStart(2, '0'))
          .join('');
      })
    );

    // Create 2FA record in database
    const { error: insertError } = await supabase
      .from('user_two_factor')
      .insert({
        user_id: user.id,
        secret: secret,
        backup_codes: hashedBackupCodes,
        enabled: true,
        created_at: new Date().toISOString()
      });

    if (insertError) {
      console.error('Error storing 2FA data:', insertError);
      return NextResponse.json(
        { error: 'Error al activar 2FA' },
        { status: 500 }
      );
    }

    // Update user metadata to indicate 2FA is enabled
    const { error: updateError } = await supabase.auth.updateUser({
      data: {
        ...user.user_metadata,
        two_factor_enabled: true,
        temp_2fa_secret: null // Remove temporary secret
      }
    });

    if (updateError) {
      console.error('Error updating user metadata:', updateError);
      // Don't fail the request, just log the error
    }

    // Log audit event
    await supabase
      .from('audit_logs')
      .insert({
        user_id: user.id,
        action: '2fa_enabled',
        resource_type: 'user',
        resource_id: user.id,
        details: { enabled: true },
        created_at: new Date().toISOString()
      });

    return NextResponse.json({
      success: true,
      message: '2FA activado exitosamente'
    });

  } catch (error) {
    console.error('Error enabling 2FA:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
