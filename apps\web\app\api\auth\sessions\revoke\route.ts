import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

/**
 * POST /api/auth/sessions/revoke
 * Revoke a specific session
 */
export async function POST(request: NextRequest) {
  try {
    const { sessionId } = await request.json();

    if (!sessionId) {
      return NextResponse.json(
        { error: 'ID de sesión requerido' },
        { status: 400 }
      );
    }

    const supabase = createRouteHandlerClient({ cookies });

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Usuario no autenticado' },
        { status: 401 }
      );
    }

    // In a real implementation, you would:
    // 1. Check if the session belongs to the current user
    // 2. Invalidate the session token
    // 3. Remove from active sessions table
    // 4. Notify other services about session revocation

    // For now, we'll log the revocation event
    const { error: logError } = await supabase
      .from('audit_logs')
      .insert({
        user_id: user.id,
        action: 'session_revoked',
        resource_type: 'session',
        resource_id: sessionId,
        details: {
          revoked_at: new Date().toISOString(),
          revoked_by: user.id
        }
      });

    if (logError) {
      console.error('Error logging session revocation:', logError);
    }

    return NextResponse.json({
      success: true,
      message: 'Sesión revocada exitosamente',
      sessionId
    });

  } catch (error) {
    console.error('Error revoking session:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
