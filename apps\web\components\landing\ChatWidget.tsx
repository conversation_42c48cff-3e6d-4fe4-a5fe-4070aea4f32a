'use client';

import { useState, useRef, useEffect } from 'react';
import { 
  ChatBubbleLeftRightIcon, 
  XMarkIcon, 
  PaperAirplaneIcon,
  SparklesIcon,
  UserIcon
} from '@heroicons/react/24/outline';

interface Message {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
}

interface ChatWidgetProps {
  isOpen: boolean;
  onToggle: () => void;
}

const welcomeMessages = [
  "¡Hola! Soy tu asistente virtual de la Alcaldía de Chía. ¿En qué puedo ayudarte hoy?",
  "Puedo ayudarte con información sobre trámites, servicios municipales, horarios de atención y mucho más.",
];

const quickActions = [
  "¿Cómo obtengo un certificado de residencia?",
  "¿Dónde puedo pagar el impuesto predial?",
  "¿Cuáles son los horarios de atención?",
  "¿Cómo solicito una licencia de construcción?",
];

export default function ChatWidget({ isOpen, onToggle }: ChatWidgetProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Initialize chat with welcome messages
  useEffect(() => {
    if (isOpen && !isInitialized) {
      const initMessages: Message[] = welcomeMessages.map((content, index) => ({
        id: `welcome-${index}`,
        content,
        isUser: false,
        timestamp: new Date(),
      }));
      setMessages(initMessages);
      setIsInitialized(true);
    }
  }, [isOpen, isInitialized]);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input when chat opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return;

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      content: content.trim(),
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);

    // Simulate AI response (in real implementation, this would call the AI API)
    setTimeout(() => {
      const aiResponse: Message = {
        id: `ai-${Date.now()}`,
        content: getAIResponse(content),
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, aiResponse]);
      setIsTyping(false);
    }, 1500);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSendMessage(inputValue);
  };

  const handleQuickAction = (action: string) => {
    handleSendMessage(action);
  };

  // Mock AI response generator
  const getAIResponse = (userMessage: string): string => {
    const lowerMessage = userMessage.toLowerCase();
    
    if (lowerMessage.includes('certificado') || lowerMessage.includes('residencia')) {
      return "Para obtener un certificado de residencia, puedes hacerlo en línea a través de nuestro portal. Solo necesitas tu documento de identidad y comprobante de domicilio. El proceso toma aproximadamente 15 minutos. ¿Te gustaría que te dirija al formulario?";
    }
    
    if (lowerMessage.includes('impuesto') || lowerMessage.includes('predial')) {
      return "El pago del impuesto predial se puede realizar en línea las 24 horas. Necesitas tu número de matrícula inmobiliaria. También puedes pagarlo en nuestras oficinas o bancos autorizados. ¿Necesitas ayuda para encontrar tu número de matrícula?";
    }
    
    if (lowerMessage.includes('horario') || lowerMessage.includes('atención')) {
      return "Nuestros horarios de atención presencial son: Lunes a Viernes de 8:00 AM a 5:00 PM, y Sábados de 8:00 AM a 12:00 PM. El portal en línea está disponible 24/7. ¿Hay algún trámite específico que necesites realizar?";
    }
    
    if (lowerMessage.includes('licencia') || lowerMessage.includes('construcción')) {
      return "Para solicitar una licencia de construcción necesitas presentar: planos arquitectónicos, estudio de suelos, y formulario de solicitud. Este trámite requiere revisión presencial. ¿Te gustaría conocer los requisitos completos?";
    }
    
    return "Gracias por tu consulta. Te puedo ayudar con información sobre trámites municipales, servicios, horarios y más. ¿Podrías ser más específico sobre lo que necesitas?";
  };

  if (!isOpen) {
    return (
      <button
        onClick={onToggle}
        className="fixed bottom-6 right-6 bg-primary-600 hover:bg-primary-700 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 z-50"
        aria-label="Abrir chat con asistente virtual"
      >
        <ChatBubbleLeftRightIcon className="h-6 w-6" />
      </button>
    );
  }

  return (
    <div className="fixed bottom-6 right-6 w-96 h-[32rem] bg-white rounded-xl shadow-2xl border border-gray-200 flex flex-col z-50">
      {/* Chat Header */}
      <div className="bg-primary-600 text-white p-4 rounded-t-xl flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
            <SparklesIcon className="h-5 w-5" />
          </div>
          <div>
            <h3 className="font-semibold">Asistente Virtual</h3>
            <p className="text-xs text-primary-100">Alcaldía de Chía</p>
          </div>
        </div>
        <button
          onClick={onToggle}
          className="text-white/80 hover:text-white p-1 rounded-full hover:bg-white/10 transition-colors focus:outline-none focus:ring-2 focus:ring-white/50"
          aria-label="Cerrar chat"
        >
          <XMarkIcon className="h-5 w-5" />
        </button>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}
          >
            <div className={`flex items-start gap-2 max-w-[80%] ${message.isUser ? 'flex-row-reverse' : 'flex-row'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                message.isUser ? 'bg-primary-600' : 'bg-gray-200'
              }`}>
                {message.isUser ? (
                  <UserIcon className="h-4 w-4 text-white" />
                ) : (
                  <SparklesIcon className="h-4 w-4 text-gray-600" />
                )}
              </div>
              <div className={`p-3 rounded-lg ${
                message.isUser 
                  ? 'bg-primary-600 text-white' 
                  : 'bg-gray-100 text-gray-900'
              }`}>
                <p className="text-sm">{message.content}</p>
                <p className={`text-xs mt-1 ${
                  message.isUser ? 'text-primary-100' : 'text-gray-500'
                }`}>
                  {message.timestamp.toLocaleTimeString('es-CO', { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  })}
                </p>
              </div>
            </div>
          </div>
        ))}

        {/* Typing Indicator */}
        {isTyping && (
          <div className="flex justify-start">
            <div className="flex items-start gap-2">
              <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                <SparklesIcon className="h-4 w-4 text-gray-600" />
              </div>
              <div className="bg-gray-100 p-3 rounded-lg">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Quick Actions */}
        {messages.length <= 2 && !isTyping && (
          <div className="space-y-2">
            <p className="text-xs text-gray-500 text-center">Preguntas frecuentes:</p>
            {quickActions.map((action, index) => (
              <button
                key={index}
                onClick={() => handleQuickAction(action)}
                className="w-full text-left p-2 text-sm text-gray-700 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                {action}
              </button>
            ))}
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="p-4 border-t border-gray-200">
        <form onSubmit={handleSubmit} className="flex gap-2">
          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder="Escribe tu pregunta..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
            disabled={isTyping}
          />
          <button
            type="submit"
            disabled={!inputValue.trim() || isTyping}
            className="bg-primary-600 hover:bg-primary-700 disabled:bg-gray-300 text-white p-2 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
            aria-label="Enviar mensaje"
          >
            <PaperAirplaneIcon className="h-4 w-4" />
          </button>
        </form>
        <p className="text-xs text-gray-500 mt-2 text-center">
          Presiona Enter para enviar
        </p>
      </div>
    </div>
  );
}
