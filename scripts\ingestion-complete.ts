#!/usr/bin/env tsx
/**
 * Procesador de Ingesta Completo CHIA - Estructura Jerárquica
 * Arquitecto: Winston 🏗️
 * Fecha: 2025-01-07
 * 
 * Estructura de Datos:
 * - Dependencias (nivel superior)
 *   - Subdependencias (nivel intermedio)
 *     - FAQs (organizadas en Temas → Preguntas)
 *     - Trámites (servicios municipales)
 *     - OPAs (Otros Procedimientos Administrativos)
 */

import fs from 'fs';
import path from 'path';

// =====================================================
// CONFIGURACIÓN
// =====================================================

const DATA_DIRECTORY = path.join(process.cwd(), 'data');
const PROJECT_ID = 'hndowofzjzjoljnapokv';

console.log('🏗️ Winston: Iniciando ingesta completa CHIA');
console.log('📁 Directorio de datos:', DATA_DIRECTORY);
console.log('🗄️ Proyecto Supabase:', PROJECT_ID);

// =====================================================
// INTERFACES DE DATOS
// =====================================================

interface FAQ {
  dependencia: string;
  codigo_dependencia: string;
  subdependencia: string;
  codigo_subdependencia: string;
  temas: {
    tema: string;
    descripcion: string;
    preguntas_frecuentes: {
      pregunta: string;
      respuesta: string;
      palabras_clave: string[];
    }[];
  }[];
}

interface Tramite {
  Nombre: string;
  Formulario: string;
  'Tiempo de respuesta': string;
  '¿Tiene pago?': string;
  'Visualización trámite en el SUIT': string;
  'Visualización trámite en GOV.CO': string;
  dependencia: string;
  subdependencia: string;
  codigo_dependencia: string;
  codigo_subdependencia: string;
}

interface OPAStructure {
  dependencias: {
    [codigo: string]: {
      nombre: string;
      sigla: string;
      subdependencias: {
        [codigo: string]: {
          nombre: string;
          sigla: string;
          OPA: {
            codigo_OPA: string;
            OPA: string;
          }[];
        };
      };
    };
  };
}

// =====================================================
// FUNCIONES DE UTILIDAD
// =====================================================

function loadJSONFile<T>(fileName: string): T {
  const filePath = path.join(DATA_DIRECTORY, fileName);
  if (!fs.existsSync(filePath)) {
    throw new Error(`Archivo no encontrado: ${fileName}`);
  }
  
  const content = fs.readFileSync(filePath, 'utf-8');
  return JSON.parse(content) as T;
}

function escapeSQL(str: string): string {
  return str.replace(/'/g, "''");
}

function generateInsertSQL(table: string, data: Record<string, any>): string {
  const columns = Object.keys(data);
  const values = columns.map(col => {
    const value = data[col];
    if (value === null || value === undefined) {
      return 'NULL';
    } else if (typeof value === 'string') {
      return `'${escapeSQL(value)}'`;
    } else if (Array.isArray(value)) {
      return `ARRAY[${value.map(v => `'${escapeSQL(String(v))}'`).join(', ')}]`;
    } else if (typeof value === 'object') {
      return `'${escapeSQL(JSON.stringify(value))}'::jsonb`;
    } else {
      return String(value);
    }
  });
  
  return `INSERT INTO ingestion.${table} (${columns.join(', ')}) VALUES (${values.join(', ')}) ON CONFLICT DO NOTHING;`;
}

// =====================================================
// PROCESADORES DE DATOS
// =====================================================

async function processDependenciasAndSubdependencias(): Promise<Map<string, string>> {
  console.log('\n📋 Procesando dependencias y subdependencias...');
  
  const dependenciaMap = new Map<string, string>();
  const subdependenciaMap = new Map<string, string>();
  
  // Cargar datos de todas las fuentes
  const faqData = loadJSONFile<{ faqs: FAQ[] }>('faqs_chia_estructurado.json');
  const tramiteData = loadJSONFile<Tramite[]>('tramites_chia_optimo.json');
  const opaData = loadJSONFile<OPAStructure>('OPA-chia-optimo.json');
  
  const sqlStatements: string[] = [];
  
  // Procesar dependencias de FAQs
  faqData.faqs.forEach(faq => {
    const depKey = `${faq.codigo_dependencia}`;
    if (!dependenciaMap.has(depKey)) {
      dependenciaMap.set(depKey, faq.dependencia);
      sqlStatements.push(generateInsertSQL('dependencias', {
        codigo: faq.codigo_dependencia,
        nombre: faq.dependencia,
        descripcion: `Dependencia: ${faq.dependencia}`,
        fuente_original: 'faqs_chia_estructurado'
      }));
    }
    
    const subdepKey = `${faq.codigo_dependencia}-${faq.codigo_subdependencia}`;
    if (!subdependenciaMap.has(subdepKey)) {
      subdependenciaMap.set(subdepKey, faq.subdependencia);
      sqlStatements.push(`
        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '${faq.codigo_dependencia}'),
          '${faq.codigo_subdependencia}',
          '${escapeSQL(faq.subdependencia)}',
          'Subdependencia: ${escapeSQL(faq.subdependencia)}',
          'faqs_chia_estructurado'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      `);
    }
  });
  
  // Procesar dependencias de Trámites
  tramiteData.forEach(tramite => {
    const depKey = `${tramite.codigo_dependencia}`;
    if (!dependenciaMap.has(depKey)) {
      dependenciaMap.set(depKey, tramite.dependencia);
      sqlStatements.push(generateInsertSQL('dependencias', {
        codigo: tramite.codigo_dependencia,
        nombre: tramite.dependencia,
        descripcion: `Dependencia: ${tramite.dependencia}`,
        fuente_original: 'tramites_chia_optimo'
      }));
    }
    
    const subdepKey = `${tramite.codigo_dependencia}-${tramite.codigo_subdependencia}`;
    if (!subdependenciaMap.has(subdepKey)) {
      subdependenciaMap.set(subdepKey, tramite.subdependencia);
      sqlStatements.push(`
        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '${tramite.codigo_dependencia}'),
          '${tramite.codigo_subdependencia}',
          '${escapeSQL(tramite.subdependencia)}',
          'Subdependencia: ${escapeSQL(tramite.subdependencia)}',
          'tramites_chia_optimo'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      `);
    }
  });
  
  // Procesar dependencias de OPAs
  Object.entries(opaData.dependencias).forEach(([codigo, dep]) => {
    const depKey = codigo;
    if (!dependenciaMap.has(depKey)) {
      dependenciaMap.set(depKey, dep.nombre);
      sqlStatements.push(generateInsertSQL('dependencias', {
        codigo: codigo,
        nombre: dep.nombre,
        descripcion: `Dependencia: ${dep.nombre} (${dep.sigla})`,
        fuente_original: 'OPA-chia-optimo'
      }));
    }
    
    Object.entries(dep.subdependencias).forEach(([subCodigo, subdep]) => {
      const subdepKey = `${codigo}-${subCodigo}`;
      if (!subdependenciaMap.has(subdepKey)) {
        subdependenciaMap.set(subdepKey, subdep.nombre);
        sqlStatements.push(`
          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '${codigo}'),
            '${subCodigo}',
            '${escapeSQL(subdep.nombre)}',
            'Subdependencia: ${escapeSQL(subdep.nombre)} (${subdep.sigla})',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        `);
      }
    });
  });
  
  console.log(`✅ Dependencias encontradas: ${dependenciaMap.size}`);
  console.log(`✅ Subdependencias encontradas: ${subdependenciaMap.size}`);
  
  // Escribir SQL a archivo para revisión
  fs.writeFileSync(
    path.join(process.cwd(), 'scripts', 'generated-dependencies.sql'),
    sqlStatements.join('\n\n'),
    'utf-8'
  );
  
  console.log('📄 SQL generado en: scripts/generated-dependencies.sql');
  
  return subdependenciaMap;
}

// =====================================================
// FUNCIÓN PRINCIPAL
// =====================================================

async function main() {
  try {
    console.log('\n🚀 Iniciando procesamiento...');
    
    // Paso 1: Procesar dependencias y subdependencias
    const subdependenciaMap = await processDependenciasAndSubdependencias();
    
    console.log('\n✅ Procesamiento completado');
    console.log('📊 Resumen:');
    console.log(`   - Dependencias únicas: ${new Set([...subdependenciaMap.keys()].map(k => k.split('-')[0])).size}`);
    console.log(`   - Subdependencias únicas: ${subdependenciaMap.size}`);
    
  } catch (error) {
    console.error('❌ Error durante el procesamiento:', error);
    process.exit(1);
  }
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  main();
}
