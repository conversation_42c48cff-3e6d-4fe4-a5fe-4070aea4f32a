import { Metadata } from 'next';
import Link from 'next/link';
import PageLayout from '@/components/layout/PageLayout';
import {
  DocumentTextIcon,
  UserGroupIcon,
  HeartIcon,
  IdentificationIcon,
  ArrowRightIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

export const metadata: Metadata = {
  title: 'Registro Civil | Portal CHIA',
  description: 'Servicios de registro civil: nacimientos, matrimonios, defunciones y otros actos del estado civil en Chía.',
  keywords: 'registro civil, nacimiento, matrimonio, defunción, estado civil, Chía',
};

const civilServices = [
  {
    id: 'nacimiento',
    name: 'Registro de Nacimiento',
    description: 'Inscripción de nacimientos ocurridos en el municipio de Chía',
    icon: UserGroupIcon,
    price: 'Gratuito',
    time: 'Inmediato',
    requirements: [
      'Certificado médico de nacido vivo',
      'Documento de identidad de los padres',
      'Registro civil de matrimonio de los padres (si aplica)',
      'Declaración de testigos (si es necesario)'
    ],
    popular: true
  },
  {
    id: 'matrimonio',
    name: 'Registro de Matrimonio Civil',
    description: 'Celebración y registro de matrimonios civiles',
    icon: HeartIcon,
    price: '$85,000',
    time: 'Programación previa',
    requirements: [
      'Documentos de identidad de los contrayentes',
      'Certificados de soltería',
      'Dos testigos mayores de edad',
      'Examen médico prenupcial'
    ],
    popular: true
  },
  {
    id: 'defuncion',
    name: 'Registro de Defunción',
    description: 'Inscripción de defunciones ocurridas en el municipio',
    icon: DocumentTextIcon,
    price: 'Gratuito',
    time: 'Inmediato',
    requirements: [
      'Certificado médico de defunción',
      'Documento de identidad del fallecido',
      'Documento de identidad del declarante',
      'Parentesco con el fallecido'
    ],
    popular: false
  },
  {
    id: 'reconocimiento',
    name: 'Reconocimiento de Paternidad',
    description: 'Reconocimiento voluntario de paternidad o maternidad',
    icon: IdentificationIcon,
    price: 'Gratuito',
    time: 'Inmediato',
    requirements: [
      'Documento de identidad del reconociente',
      'Registro civil de nacimiento del menor',
      'Presencia del menor (si es mayor de 12 años)',
      'Consentimiento de la madre (si aplica)'
    ],
    popular: false
  }
];

const appointmentSlots = [
  { time: '8:00 AM', available: true },
  { time: '9:00 AM', available: false },
  { time: '10:00 AM', available: true },
  { time: '11:00 AM', available: true },
  { time: '2:00 PM', available: true },
  { time: '3:00 PM', available: false },
  { time: '4:00 PM', available: true }
];

export default function RegistroPage() {
  return (
    <PageLayout className="bg-gray-50">
      {/* Breadcrumb */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-4">
              <li>
                <Link href="/" className="text-gray-400 hover:text-gray-500">
                  Inicio
                </Link>
              </li>
              <li>
                <ArrowRightIcon className="h-4 w-4 text-gray-400" />
              </li>
              <li>
                <Link href="/servicios" className="text-gray-400 hover:text-gray-500">
                  Servicios
                </Link>
              </li>
              <li>
                <ArrowRightIcon className="h-4 w-4 text-gray-400" />
              </li>
              <li>
                <span className="text-gray-900 font-medium">Registro Civil</span>
              </li>
            </ol>
          </nav>
        </div>
      </div>

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-purple-600 to-purple-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <DocumentTextIcon className="h-16 w-16 mx-auto mb-4 text-purple-200" />
            <h1 className="text-4xl font-bold mb-4">
              Registro Civil
            </h1>
            <p className="text-xl text-purple-100 mb-8 max-w-3xl mx-auto">
              Servicios de registro civil para todos los actos del estado civil. 
              Atención personalizada y trámites ágiles para momentos importantes de tu vida.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Services Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {civilServices.map((service) => (
            <div key={service.id} className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 hover:shadow-lg transition-shadow">
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="p-3 bg-purple-100 rounded-lg">
                      <service.icon className="h-6 w-6 text-purple-600" />
                    </div>
                    {service.popular && (
                      <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">
                        Popular
                      </span>
                    )}
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-purple-600">{service.price}</div>
                    <div className="text-sm text-gray-500">Costo</div>
                  </div>
                </div>
                
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {service.name}
                </h3>
                
                <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                  {service.description}
                </p>
                
                <div className="flex items-center gap-4 text-sm text-gray-500 mb-6">
                  <div className="flex items-center gap-1">
                    <ClockIcon className="h-4 w-4" />
                    {service.time}
                  </div>
                </div>
                
                <div className="mb-6">
                  <h4 className="font-medium text-gray-900 mb-3">Requisitos:</h4>
                  <ul className="space-y-2">
                    {service.requirements.map((req, index) => (
                      <li key={index} className="flex items-start gap-2 text-sm text-gray-600">
                        <CheckCircleIcon className="h-4 w-4 text-green-500 flex-shrink-0 mt-0.5" />
                        {req}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
              
              <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
                <button className="w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2">
                  Agendar Cita
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Appointment Booking */}
        <div className="bg-white rounded-xl shadow-md p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            Agenda tu Cita
          </h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
              <h3 className="font-semibold text-gray-900 mb-4">Selecciona el servicio</h3>
              <select className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent mb-4">
                <option value="">Seleccionar servicio...</option>
                <option value="nacimiento">Registro de Nacimiento</option>
                <option value="matrimonio">Matrimonio Civil</option>
                <option value="defuncion">Registro de Defunción</option>
                <option value="reconocimiento">Reconocimiento de Paternidad</option>
              </select>
              
              <h3 className="font-semibold text-gray-900 mb-4">Fecha preferida</h3>
              <input
                type="date"
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent mb-4"
                min={new Date().toISOString().split('T')[0]}
              />
            </div>
            
            <div>
              <h3 className="font-semibold text-gray-900 mb-4">Horarios disponibles</h3>
              <div className="grid grid-cols-2 gap-3">
                {appointmentSlots.map((slot, index) => (
                  <button
                    key={index}
                    disabled={!slot.available}
                    className={`p-3 rounded-lg text-sm font-medium transition-colors ${
                      slot.available
                        ? 'bg-purple-100 hover:bg-purple-200 text-purple-700 border border-purple-300'
                        : 'bg-gray-100 text-gray-400 cursor-not-allowed border border-gray-200'
                    }`}
                  >
                    {slot.time}
                    {!slot.available && (
                      <span className="block text-xs">No disponible</span>
                    )}
                  </button>
                ))}
              </div>
              
              <button className="w-full mt-6 bg-purple-600 hover:bg-purple-700 text-white px-4 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2">
                Confirmar Cita
              </button>
            </div>
          </div>
        </div>

        {/* Important Information */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <h3 className="font-semibold text-blue-900 mb-4">
            Información Importante sobre Registro Civil
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-blue-800 text-sm">
            <ul className="space-y-2">
              <li className="flex items-start gap-2">
                <CheckCircleIcon className="h-4 w-4 text-blue-600 flex-shrink-0 mt-0.5" />
                Los registros de nacimiento y defunción son gratuitos por ley
              </li>
              <li className="flex items-start gap-2">
                <CheckCircleIcon className="h-4 w-4 text-blue-600 flex-shrink-0 mt-0.5" />
                Para matrimonios civiles se requiere programación previa
              </li>
            </ul>
            <ul className="space-y-2">
              <li className="flex items-start gap-2">
                <CheckCircleIcon className="h-4 w-4 text-blue-600 flex-shrink-0 mt-0.5" />
                Todos los documentos deben estar vigentes y en buen estado
              </li>
              <li className="flex items-start gap-2">
                <CheckCircleIcon className="h-4 w-4 text-blue-600 flex-shrink-0 mt-0.5" />
                Se expiden copias auténticas inmediatamente
              </li>
            </ul>
          </div>
        </div>

        {/* Contact and Help */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="bg-white rounded-xl shadow-md p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              Horarios de Atención
            </h3>
            <div className="space-y-3 text-gray-600">
              <div className="flex justify-between">
                <span>Lunes a Viernes:</span>
                <span className="font-medium">8:00 AM - 5:00 PM</span>
              </div>
              <div className="flex justify-between">
                <span>Sábados:</span>
                <span className="font-medium">8:00 AM - 12:00 PM</span>
              </div>
              <div className="flex justify-between">
                <span>Domingos:</span>
                <span className="text-red-600">Cerrado</span>
              </div>
            </div>
            
            <div className="mt-6 p-4 bg-purple-50 rounded-lg">
              <p className="text-purple-800 text-sm">
                <strong>Nota:</strong> Para matrimonios civiles también atendemos 
                fines de semana con cita previa.
              </p>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-md p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              ¿Necesitas ayuda?
            </h3>
            <p className="text-gray-600 mb-4">
              Nuestro equipo especializado en registro civil está disponible 
              para orientarte en todos los trámites.
            </p>
            <div className="space-y-3">
              <Link
                href="/chat"
                className="block w-full text-center bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
              >
                Consultar con IA
              </Link>
              <Link
                href="/contacto"
                className="block w-full text-center bg-white hover:bg-gray-50 text-purple-600 border border-purple-600 px-4 py-2 rounded-lg font-medium transition-colors"
              >
                Contactar Oficina
              </Link>
            </div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
