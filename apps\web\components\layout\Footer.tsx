import Link from 'next/link';
import { 
  PhoneIcon, 
  EnvelopeIcon, 
  MapPinIcon,
  GlobeAltIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  CreditCardIcon,
  BuildingOfficeIcon,
  IdentificationIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const quickLinks = [
    { name: 'Inicio', href: '/' },
    { name: 'Servic<PERSON>', href: '/servicios' },
    { name: 'Asistente IA', href: '/chat' },
    { name: '<PERSON><PERSON>', href: '/contacto' },
    { name: 'Acerca de', href: '/acerca' },
  ];

  const services = [
    { name: 'Certificados', href: '/servicios/certificados', icon: DocumentTextIcon },
    { name: 'Pagos en Línea', href: '/servicios/pagos', icon: CreditCardIcon },
    { name: 'Licencias', href: '/servicios/licencias', icon: BuildingOfficeIcon },
    { name: 'Registro Civil', href: '/servicios/registro', icon: IdentificationIcon },
    { name: 'Consultas', href: '/servicios/consultas', icon: MagnifyingGlassIcon },
  ];

  const contactInfo = [
    {
      icon: PhoneIcon,
      label: 'Teléfono',
      value: '(*************',
      href: 'tel:+576018845911'
    },
    {
      icon: EnvelopeIcon,
      label: 'Email',
      value: '<EMAIL>',
      href: 'mailto:<EMAIL>'
    },
    {
      icon: MapPinIcon,
      label: 'Dirección',
      value: 'Carrera 11 No. 17-25, Chía, Cundinamarca',
      href: 'https://maps.google.com/?q=Carrera+11+No.+17-25+Chía+Cundinamarca'
    },
    {
      icon: GlobeAltIcon,
      label: 'Sitio Web',
      value: 'www.chia-cundinamarca.gov.co',
      href: 'https://www.chia-cundinamarca.gov.co'
    }
  ];

  const legalLinks = [
    { name: 'Términos y Condiciones', href: '/terminos' },
    { name: 'Política de Privacidad', href: '/privacidad' },
    { name: 'Política de Tratamiento de Datos', href: '/datos' },
    { name: 'Mapa del Sitio', href: '/sitemap' },
  ];

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand & Description */}
          <div className="lg:col-span-1">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">C</span>
              </div>
              <div>
                <span className="text-2xl font-bold">CHIA</span>
                <span className="text-sm text-gray-300 block leading-none">Portal Ciudadano</span>
              </div>
            </div>
            <p className="text-gray-300 text-sm mb-6">
              Portal oficial de servicios ciudadanos para el municipio de Chía, Cundinamarca. 
              Transformando la relación entre ciudadanos y gobierno a través de la innovación digital.
            </p>
            <Link
              href="/chat"
              className="inline-flex items-center gap-2 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-gray-900"
            >
              <ChatBubbleLeftRightIcon className="h-4 w-4" />
              Asistente IA 24/7
            </Link>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Enlaces Rápidos</h3>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-white text-sm transition-colors focus:outline-none focus:text-white"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Servicios</h3>
            <ul className="space-y-2">
              {services.map((service) => (
                <li key={service.name}>
                  <Link
                    href={service.href}
                    className="flex items-center gap-2 text-gray-300 hover:text-white text-sm transition-colors focus:outline-none focus:text-white"
                  >
                    <service.icon className="h-4 w-4" />
                    {service.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Contacto</h3>
            <ul className="space-y-3">
              {contactInfo.map((contact) => (
                <li key={contact.label}>
                  <a
                    href={contact.href}
                    target={contact.href.startsWith('http') ? '_blank' : undefined}
                    rel={contact.href.startsWith('http') ? 'noopener noreferrer' : undefined}
                    className="flex items-start gap-2 text-gray-300 hover:text-white text-sm transition-colors focus:outline-none focus:text-white"
                  >
                    <contact.icon className="h-4 w-4 mt-0.5 flex-shrink-0" />
                    <div>
                      <div className="font-medium">{contact.label}</div>
                      <div className="text-xs text-gray-400">{contact.value}</div>
                    </div>
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Divider */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            {/* Legal Links */}
            <div className="flex flex-wrap justify-center md:justify-start gap-4">
              {legalLinks.map((link) => (
                <Link
                  key={link.name}
                  href={link.href}
                  className="text-gray-400 hover:text-white text-xs transition-colors focus:outline-none focus:text-white"
                >
                  {link.name}
                </Link>
              ))}
            </div>

            {/* Copyright */}
            <div className="text-center md:text-right">
              <p className="text-gray-400 text-xs">
                © {currentYear} Municipio de Chía, Cundinamarca. Todos los derechos reservados.
              </p>
              <p className="text-gray-500 text-xs mt-1">
                Desarrollado con ❤️ para los ciudadanos de Chía
              </p>
            </div>
          </div>
        </div>

        {/* Government Compliance */}
        <div className="mt-6 pt-6 border-t border-gray-800">
          <div className="text-center">
            <p className="text-gray-400 text-xs mb-2">
              Portal oficial del gobierno municipal - Cumple con estándares de accesibilidad WCAG 2.2 AA
            </p>
            <div className="flex justify-center items-center gap-4 text-xs text-gray-500">
              <span>🔒 Sitio Seguro SSL</span>
              <span>♿ Accesible</span>
              <span>📱 Responsive</span>
              <span>🤖 IA Integrada</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
