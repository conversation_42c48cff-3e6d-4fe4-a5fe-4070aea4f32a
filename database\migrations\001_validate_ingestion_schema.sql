-- =====================================================
-- VALIDACIÓN: Esquema de Ingesta de Datos CHIA
-- Versión: 001
-- Fecha: 2025-01-07
-- Descripción: Script para validar que la migración 001
--              se ejecutó correctamente
-- =====================================================

-- =====================================================
-- VALIDACIÓN DE ESQUEMA
-- =====================================================

DO $$
DECLARE
    schema_exists BOOLEAN;
BEGIN
    SELECT EXISTS(
        SELECT 1 FROM information_schema.schemata 
        WHERE schema_name = 'ingestion'
    ) INTO schema_exists;
    
    IF schema_exists THEN
        RAISE NOTICE '✓ Esquema "ingestion" existe';
    ELSE
        RAISE EXCEPTION '✗ ERROR: Esquema "ingestion" no existe';
    END IF;
END $$;

-- =====================================================
-- VALIDACIÓN DE TABLAS
-- =====================================================

DO $$
DECLARE
    tabla_record RECORD;
    tablas_esperadas TEXT[] := ARRAY[
        'dependencias',
        'subdependencias', 
        'faqs',
        'tramites',
        'opas',
        'ingestion_logs'
    ];
    tabla_nombre TEXT;
    tabla_existe BOOLEAN;
    contador INTEGER := 0;
BEGIN
    FOREACH tabla_nombre IN ARRAY tablas_esperadas
    LOOP
        SELECT EXISTS(
            SELECT 1 FROM information_schema.tables 
            WHERE table_schema = 'ingestion' 
            AND table_name = tabla_nombre
        ) INTO tabla_existe;
        
        IF tabla_existe THEN
            RAISE NOTICE '✓ Tabla "ingestion.%" existe', tabla_nombre;
            contador := contador + 1;
        ELSE
            RAISE EXCEPTION '✗ ERROR: Tabla "ingestion.%" no existe', tabla_nombre;
        END IF;
    END LOOP;
    
    RAISE NOTICE '✓ Todas las % tablas principales creadas correctamente', contador;
END $$;

-- =====================================================
-- VALIDACIÓN DE COLUMNAS CRÍTICAS
-- =====================================================

DO $$
DECLARE
    columna_existe BOOLEAN;
BEGIN
    -- Validar columnas UUID en tablas principales
    SELECT EXISTS(
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'ingestion' 
        AND table_name = 'dependencias' 
        AND column_name = 'id' 
        AND data_type = 'uuid'
    ) INTO columna_existe;
    
    IF columna_existe THEN
        RAISE NOTICE '✓ Columnas UUID configuradas correctamente';
    ELSE
        RAISE EXCEPTION '✗ ERROR: Columnas UUID no configuradas correctamente';
    END IF;
    
    -- Validar columnas de búsqueda
    SELECT EXISTS(
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'ingestion' 
        AND table_name = 'faqs' 
        AND column_name = 'vector_busqueda' 
        AND data_type = 'tsvector'
    ) INTO columna_existe;
    
    IF columna_existe THEN
        RAISE NOTICE '✓ Columnas de búsqueda configuradas correctamente';
    ELSE
        RAISE EXCEPTION '✗ ERROR: Columnas de búsqueda no configuradas correctamente';
    END IF;
END $$;

-- =====================================================
-- VALIDACIÓN DE ÍNDICES
-- =====================================================

DO $$
DECLARE
    indices_count INTEGER;
BEGIN
    SELECT COUNT(*) FROM pg_indexes 
    WHERE schemaname = 'ingestion'
    INTO indices_count;
    
    IF indices_count >= 20 THEN
        RAISE NOTICE '✓ Índices creados correctamente (% índices)', indices_count;
    ELSE
        RAISE WARNING '⚠ Advertencia: Solo % índices encontrados, se esperaban al menos 20', indices_count;
    END IF;
END $$;

-- =====================================================
-- VALIDACIÓN DE FUNCIONES
-- =====================================================

DO $$
DECLARE
    funciones_esperadas TEXT[] := ARRAY[
        'update_updated_at_column',
        'update_faqs_search_vector',
        'update_tramites_search_vector',
        'update_opas_search_vector',
        'buscar_contenido',
        'obtener_estadisticas_ingesta',
        'validar_integridad_datos'
    ];
    funcion_nombre TEXT;
    funcion_existe BOOLEAN;
    contador INTEGER := 0;
BEGIN
    FOREACH funcion_nombre IN ARRAY funciones_esperadas
    LOOP
        SELECT EXISTS(
            SELECT 1 FROM information_schema.routines 
            WHERE routine_schema = 'ingestion' 
            AND routine_name = funcion_nombre
        ) INTO funcion_existe;
        
        IF funcion_existe THEN
            contador := contador + 1;
        ELSE
            RAISE EXCEPTION '✗ ERROR: Función "ingestion.%" no existe', funcion_nombre;
        END IF;
    END LOOP;
    
    RAISE NOTICE '✓ Todas las % funciones creadas correctamente', contador;
END $$;

-- =====================================================
-- VALIDACIÓN DE TRIGGERS
-- =====================================================

DO $$
DECLARE
    triggers_count INTEGER;
BEGIN
    SELECT COUNT(*) FROM information_schema.triggers 
    WHERE trigger_schema = 'ingestion'
    INTO triggers_count;
    
    IF triggers_count >= 8 THEN
        RAISE NOTICE '✓ Triggers creados correctamente (% triggers)', triggers_count;
    ELSE
        RAISE WARNING '⚠ Advertencia: Solo % triggers encontrados, se esperaban al menos 8', triggers_count;
    END IF;
END $$;

-- =====================================================
-- VALIDACIÓN DE POLÍTICAS RLS
-- =====================================================

DO $$
DECLARE
    rls_habilitado BOOLEAN;
    politicas_count INTEGER;
BEGIN
    -- Verificar que RLS esté habilitado
    SELECT COUNT(*) > 0 FROM pg_class c
    JOIN pg_namespace n ON c.relnamespace = n.oid
    WHERE n.nspname = 'ingestion' 
    AND c.relrowsecurity = true
    INTO rls_habilitado;
    
    IF rls_habilitado THEN
        RAISE NOTICE '✓ RLS habilitado en las tablas';
    ELSE
        RAISE EXCEPTION '✗ ERROR: RLS no está habilitado correctamente';
    END IF;
    
    -- Contar políticas
    SELECT COUNT(*) FROM pg_policy p
    JOIN pg_class c ON p.polrelid = c.oid
    JOIN pg_namespace n ON c.relnamespace = n.oid
    WHERE n.nspname = 'ingestion'
    INTO politicas_count;
    
    IF politicas_count >= 12 THEN
        RAISE NOTICE '✓ Políticas RLS creadas correctamente (% políticas)', politicas_count;
    ELSE
        RAISE WARNING '⚠ Advertencia: Solo % políticas encontradas, se esperaban al menos 12', politicas_count;
    END IF;
END $$;

-- =====================================================
-- VALIDACIÓN DE VISTAS
-- =====================================================

DO $$
DECLARE
    vistas_esperadas TEXT[] := ARRAY[
        'vista_estructura_organizacional',
        'vista_faqs_completa'
    ];
    vista_nombre TEXT;
    vista_existe BOOLEAN;
    contador INTEGER := 0;
BEGIN
    FOREACH vista_nombre IN ARRAY vistas_esperadas
    LOOP
        SELECT EXISTS(
            SELECT 1 FROM information_schema.views 
            WHERE table_schema = 'ingestion' 
            AND table_name = vista_nombre
        ) INTO vista_existe;
        
        IF vista_existe THEN
            contador := contador + 1;
        ELSE
            RAISE EXCEPTION '✗ ERROR: Vista "ingestion.%" no existe', vista_nombre;
        END IF;
    END LOOP;
    
    RAISE NOTICE '✓ Todas las % vistas creadas correctamente', contador;
END $$;

-- =====================================================
-- VALIDACIÓN DE EXTENSIONES
-- =====================================================

DO $$
DECLARE
    extension_exists BOOLEAN;
BEGIN
    SELECT EXISTS(
        SELECT 1 FROM pg_extension WHERE extname = 'uuid-ossp'
    ) INTO extension_exists;
    
    IF extension_exists THEN
        RAISE NOTICE '✓ Extensión uuid-ossp instalada';
    ELSE
        RAISE EXCEPTION '✗ ERROR: Extensión uuid-ossp no instalada';
    END IF;
    
    SELECT EXISTS(
        SELECT 1 FROM pg_extension WHERE extname = 'pg_trgm'
    ) INTO extension_exists;
    
    IF extension_exists THEN
        RAISE NOTICE '✓ Extensión pg_trgm instalada';
    ELSE
        RAISE EXCEPTION '✗ ERROR: Extensión pg_trgm no instalada';
    END IF;
END $$;

-- =====================================================
-- PRUEBA FUNCIONAL BÁSICA
-- =====================================================

DO $$
DECLARE
    test_id UUID;
BEGIN
    -- Insertar datos de prueba
    INSERT INTO ingestion.dependencias (codigo, nombre, sigla)
    VALUES ('999', 'Dependencia de Prueba', 'DP')
    RETURNING id INTO test_id;
    
    -- Verificar que se insertó correctamente
    IF test_id IS NOT NULL THEN
        RAISE NOTICE '✓ Inserción de prueba exitosa';
        
        -- Limpiar datos de prueba
        DELETE FROM ingestion.dependencias WHERE id = test_id;
        RAISE NOTICE '✓ Limpieza de datos de prueba exitosa';
    ELSE
        RAISE EXCEPTION '✗ ERROR: Fallo en inserción de prueba';
    END IF;
END $$;

-- =====================================================
-- RESUMEN FINAL
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '================================================';
    RAISE NOTICE '✅ VALIDACIÓN COMPLETADA EXITOSAMENTE';
    RAISE NOTICE '================================================';
    RAISE NOTICE 'El esquema de ingesta está listo para usar';
    RAISE NOTICE 'Puede proceder con la ingesta de datos';
    RAISE NOTICE '================================================';
END $$;
