'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { LoginForm } from '@/components/auth/login-form';
import { RegisterForm } from '@/components/auth/register-form';
import { ForgotPasswordForm } from '@/components/auth/forgot-password-form';
import { AnimatedBeam } from '@/components/ui/animated-beam';
import { useRef } from 'react';

type AuthView = 'login' | 'register' | 'forgot-password';

/**
 * Login page client component
 * Handles authentication flow with multiple views
 */
export function LoginPageClient() {
  const router = useRouter();
  const [currentView, setCurrentView] = useState<AuthView>('login');
  
  // Refs for animated beam effect
  const containerRef = useRef<HTMLDivElement>(null);
  const logoRef = useRef<HTMLDivElement>(null);
  const formRef = useRef<HTMLDivElement>(null);

  /**
   * Handle successful authentication
   */
  const handleAuthSuccess = () => {
    // Redirect to dashboard or intended page
    const redirectTo = new URLSearchParams(window.location.search).get('redirect') || '/dashboard';
    router.push(redirectTo);
  };

  /**
   * Handle successful registration
   */
  const handleRegistrationSuccess = () => {
    // Show success message and redirect to email verification
    router.push('/auth/verify-email');
  };

  /**
   * Render current view based on state
   */
  const renderCurrentView = () => {
    switch (currentView) {
      case 'login':
        return (
          <LoginForm
            onSuccess={handleAuthSuccess}
            onSwitchToRegister={() => setCurrentView('register')}
            onForgotPassword={() => setCurrentView('forgot-password')}
          />
        );
      
      case 'register':
        return (
          <RegisterForm
            onSuccess={handleRegistrationSuccess}
            onSwitchToLogin={() => setCurrentView('login')}
          />
        );
      
      case 'forgot-password':
        return (
          <ForgotPasswordForm
            onBackToLogin={() => setCurrentView('login')}
            onSuccess={() => setCurrentView('login')}
          />
        );
      
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5" />
      
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-green-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000" />
        <div className="absolute top-40 left-40 w-80 h-80 bg-yellow-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000" />
      </div>

      {/* Main Content */}
      <div 
        ref={containerRef}
        className="relative z-10 flex min-h-screen"
      >
        {/* Left Side - Branding */}
        <div className="hidden lg:flex lg:w-1/2 flex-col justify-center items-center p-12 bg-gradient-to-br from-primary/10 to-primary/5">
          <div ref={logoRef} className="text-center space-y-6 max-w-md">
            {/* Logo */}
            <div className="mx-auto w-24 h-24 bg-primary rounded-full flex items-center justify-center">
              <svg
                className="w-12 h-12 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                />
              </svg>
            </div>

            {/* Title */}
            <div>
              <h1 className="text-4xl font-bold text-gray-900 mb-2">
                Portal Ciudadano Digital
              </h1>
              <p className="text-xl text-gray-600">
                Municipio de Chía
              </p>
            </div>

            {/* Description */}
            <div className="space-y-4 text-gray-600">
              <p className="text-lg">
                Tu puerta de entrada a todos los servicios municipales digitales
              </p>
              
              <div className="grid grid-cols-1 gap-3 text-sm">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                  <span>Trámites en línea 24/7</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full" />
                  <span>Seguimiento en tiempo real</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-purple-500 rounded-full" />
                  <span>Notificaciones automáticas</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-orange-500 rounded-full" />
                  <span>Soporte especializado</span>
                </div>
              </div>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-4 pt-6 border-t border-gray-200">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">50+</div>
                <div className="text-xs text-gray-500">Servicios</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">24/7</div>
                <div className="text-xs text-gray-500">Disponible</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">100%</div>
                <div className="text-xs text-gray-500">Digital</div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Side - Authentication Form */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-6 lg:p-12">
          <div ref={formRef} className="w-full max-w-md lg:max-w-2xl">
            {renderCurrentView()}
          </div>
        </div>

        {/* Animated Beam Connection */}
        <AnimatedBeam
          containerRef={containerRef}
          fromRef={logoRef}
          toRef={formRef}
          curvature={-50}
          duration={3}
          pathOpacity={0.1}
          gradientStartColor="#3b82f6"
          gradientStopColor="#10b981"
        />
      </div>

      {/* Footer */}
      <div className="absolute bottom-0 left-0 right-0 p-6 text-center text-sm text-gray-500 bg-white/80 backdrop-blur-sm border-t">
        <div className="flex flex-col sm:flex-row justify-center items-center gap-4">
          <span>© 2024 Municipio de Chía. Todos los derechos reservados.</span>
          <div className="flex gap-4">
            <a href="/terminos" className="hover:text-primary transition-colors">
              Términos
            </a>
            <a href="/privacidad" className="hover:text-primary transition-colors">
              Privacidad
            </a>
            <a href="/soporte" className="hover:text-primary transition-colors">
              Soporte
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
