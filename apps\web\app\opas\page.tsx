'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import MainNavigation from '@/components/layout/MainNavigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Search, 
  Users, 
  Building2, 
  ChevronRight,
  Filter,
  Clock,
  FileText,
  AlertCircle
} from 'lucide-react';
import Link from 'next/link';

interface OPA {
  id: string;
  nombre: string;
  descripcion: string;
  categoria: string;
  dependencia_nombre: string;
  subdependencia_nombre: string;
  objetivo: string;
  alcance: string;
  responsable: string;
  estado: string;
  version: string;
  fecha_aprobacion: string;
  created_at: string;
}

interface ApiResponse {
  success: boolean;
  data: OPA[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  error?: string;
}

export default function OPAsPage() {
  const searchParams = useSearchParams();
  const dependenciaFilter = searchParams.get('dependencia');
  const subdependenciaFilter = searchParams.get('subdependencia');
  
  const [opas, setOpas] = useState<OPA[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategoria, setSelectedCategoria] = useState<string>('');
  const [selectedDependencia, setSelectedDependencia] = useState<string>(dependenciaFilter || '');
  const [categorias, setCategorias] = useState<string[]>([]);
  const [dependencias, setDependencias] = useState<{id: string, nombre: string}[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 12,
    total: 0,
    totalPages: 0
  });

  useEffect(() => {
    fetchOPAs();
    fetchCategorias();
    fetchDependencias();
  }, [currentPage, searchTerm, selectedCategoria, selectedDependencia]);

  const fetchOPAs = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '12'
      });

      if (searchTerm) params.append('search', searchTerm);
      if (selectedCategoria) params.append('categoria', selectedCategoria);
      if (selectedDependencia) params.append('dependencia', selectedDependencia);
      if (subdependenciaFilter) params.append('subdependencia', subdependenciaFilter);

      const response = await fetch(`/api/opas?${params}`);
      
      if (!response.ok) {
        throw new Error('Error al cargar OPAs');
      }

      const data: ApiResponse = await response.json();
      
      if (data.success) {
        setOpas(data.data);
        setPagination(data.pagination);
      } else {
        throw new Error(data.error || 'Error desconocido');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setLoading(false);
    }
  };

  const fetchCategorias = async () => {
    try {
      const response = await fetch('/api/opas/categorias');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setCategorias(data.data);
        }
      }
    } catch (err) {
      console.error('Error al cargar categorías:', err);
    }
  };

  const fetchDependencias = async () => {
    try {
      const response = await fetch('/api/dependencias?limit=100');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setDependencias(data.data.map((dep: any) => ({
            id: dep.id,
            nombre: dep.nombre
          })));
        }
      }
    } catch (err) {
      console.error('Error al cargar dependencias:', err);
    }
  };

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  const handleCategoriaChange = (value: string) => {
    setSelectedCategoria(value === 'all' ? '' : value);
    setCurrentPage(1);
  };

  const handleDependenciaChange = (value: string) => {
    setSelectedDependencia(value === 'all' ? '' : value);
    setCurrentPage(1);
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedCategoria('');
    setSelectedDependencia('');
    setCurrentPage(1);
  };

  if (loading && opas.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50">
        <MainNavigation />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Cargando OPAs...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <MainNavigation />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="text-red-500 text-xl mb-4">⚠️</div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Error al cargar</h2>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={fetchOPAs}>
                Reintentar
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <MainNavigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        {(dependenciaFilter || subdependenciaFilter) && (
          <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-6">
            <Link href="/dependencias" className="hover:text-blue-600">
              Dependencias
            </Link>
            {dependenciaFilter && (
              <>
                <ChevronRight className="h-4 w-4" />
                <Link href={`/dependencias/${dependenciaFilter}`} className="hover:text-blue-600">
                  {dependencias.find(d => d.id === dependenciaFilter)?.nombre || 'Dependencia'}
                </Link>
              </>
            )}
            <ChevronRight className="h-4 w-4" />
            <span className="text-gray-900">OPAs</span>
          </nav>
        )}

        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-2">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Users className="h-6 w-6 text-purple-600" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900">
              Otros Procedimientos Administrativos (OPAs)
            </h1>
          </div>
          <p className="text-gray-600">
            Consulta los procedimientos administrativos internos del municipio de Chía.
          </p>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder="Buscar OPAs..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={selectedCategoria || 'all'} onValueChange={handleCategoriaChange}>
              <SelectTrigger>
                <SelectValue placeholder="Todas las categorías" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas las categorías</SelectItem>
                {categorias.map((categoria) => (
                  <SelectItem key={categoria} value={categoria}>
                    {categoria}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedDependencia || 'all'} onValueChange={handleDependenciaChange}>
              <SelectTrigger>
                <SelectValue placeholder="Todas las dependencias" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas las dependencias</SelectItem>
                {dependencias.map((dependencia) => (
                  <SelectItem key={dependencia.id} value={dependencia.id}>
                    {dependencia.nombre}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Button variant="outline" onClick={clearFilters}>
              <Filter className="h-4 w-4 mr-2" />
              Limpiar Filtros
            </Button>
          </div>
        </div>

        {/* Results Summary */}
        <div className="flex items-center justify-between mb-6">
          <p className="text-gray-600">
            Mostrando {opas.length} de {pagination.total} OPAs
          </p>
          <div className="text-sm text-gray-500">
            Página {pagination.page} de {pagination.totalPages}
          </div>
        </div>

        {/* OPAs Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {opas.map((opa) => (
            <Card key={opa.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <CardTitle className="text-lg line-clamp-2">
                    <Link 
                      href={`/opas/${opa.id}`}
                      className="hover:text-purple-600 transition-colors"
                    >
                      {opa.nombre}
                    </Link>
                  </CardTitle>
                  <Badge variant="secondary" className="ml-2 shrink-0">
                    {opa.categoria}
                  </Badge>
                </div>
                {opa.descripcion && (
                  <CardDescription className="line-clamp-3">
                    {opa.descripcion}
                  </CardDescription>
                )}
              </CardHeader>
              
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center text-sm text-gray-600">
                    <Building2 className="h-4 w-4 mr-2" />
                    <span className="truncate">
                      {opa.dependencia_nombre}
                      {opa.subdependencia_nombre && ` - ${opa.subdependencia_nombre}`}
                    </span>
                  </div>
                  
                  {opa.responsable && (
                    <div className="flex items-center text-sm text-gray-600">
                      <Users className="h-4 w-4 mr-2" />
                      <span className="truncate">{opa.responsable}</span>
                    </div>
                  )}
                  
                  {opa.version && (
                    <div className="flex items-center text-sm text-gray-600">
                      <FileText className="h-4 w-4 mr-2" />
                      <span>Versión {opa.version}</span>
                    </div>
                  )}
                  
                  <div className="flex items-center justify-between pt-2 border-t">
                    <Badge 
                      variant={opa.estado === 'activo' ? 'default' : 'secondary'}
                      className="text-xs"
                    >
                      {opa.estado}
                    </Badge>
                    <Link href={`/opas/${opa.id}`}>
                      <Button variant="outline" size="sm">
                        Ver Detalle
                      </Button>
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="flex items-center justify-center space-x-2">
            <Button
              variant="outline"
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1 || loading}
            >
              Anterior
            </Button>
            
            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                const page = i + 1;
                return (
                  <Button
                    key={page}
                    variant={currentPage === page ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCurrentPage(page)}
                    disabled={loading}
                  >
                    {page}
                  </Button>
                );
              })}
            </div>
            
            <Button
              variant="outline"
              onClick={() => setCurrentPage(Math.min(pagination.totalPages, currentPage + 1))}
              disabled={currentPage === pagination.totalPages || loading}
            >
              Siguiente
            </Button>
          </div>
        )}

        {opas.length === 0 && !loading && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">👥</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              No se encontraron OPAs
            </h3>
            <p className="text-gray-600 mb-4">
              Intenta ajustar los filtros de búsqueda
            </p>
            <Button onClick={clearFilters}>
              Limpiar Filtros
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
