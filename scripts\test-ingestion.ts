#!/usr/bin/env tsx
/**
 * Script de prueba simplificado para la ingesta de datos CHIA
 * Arquitecto: Winston 🏗️
 * Fecha: 2025-01-07
 */

import fs from 'fs';
import path from 'path';

// =====================================================
// CONFIGURACIÓN
// =====================================================

const DATA_DIRECTORY = path.join(process.cwd(), 'data');

console.log('🏗️ Winston: Iniciando prueba de ingesta CHIA');
console.log('📁 Directorio de datos:', DATA_DIRECTORY);

// =====================================================
// VERIFICAR ARCHIVOS DE DATOS
// =====================================================

const requiredFiles = [
  'faqs_chia_estructurado.json',
  'tramites_chia_optimo.json', 
  'OPA-chia-optimo.json'
];

console.log('\n📋 Verificando archivos de datos...');

for (const fileName of requiredFiles) {
  const filePath = path.join(DATA_DIRECTORY, fileName);
  
  if (fs.existsSync(filePath)) {
    const stats = fs.statSync(filePath);
    console.log(`✅ ${fileName} - ${(stats.size / 1024).toFixed(2)} KB`);
    
    try {
      const content = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
      
      if (fileName === 'faqs_chia_estructurado.json') {
        const faqsArray = content.faqs || [];
        console.log(`   📊 FAQs encontradas: ${faqsArray.length || 0}`);
        if (faqsArray.length > 0) {
          // Contar total de preguntas en todos los temas
          let totalPreguntas = 0;
          faqsArray.forEach((dep: any) => {
            dep.temas?.forEach((tema: any) => {
              totalPreguntas += tema.preguntas_frecuentes?.length || 0;
            });
          });
          console.log(`   📝 Total preguntas: ${totalPreguntas}`);
          console.log(`   📝 Ejemplo dependencia: ${faqsArray[0].dependencia?.substring(0, 50)}...`);
        }
      } else if (fileName === 'tramites_chia_optimo.json') {
        console.log(`   📊 Trámites encontrados: ${content.length || 0}`);
        if (content.length > 0) {
          console.log(`   📝 Ejemplo: ${content[0].nombre?.substring(0, 50)}...`);
        }
      } else if (fileName === 'OPA-chia-optimo.json') {
        const dependencias = content.dependencias || {};
        let totalOPAs = 0;
        Object.values(dependencias).forEach((dep: any) => {
          Object.values(dep.subdependencias || {}).forEach((subdep: any) => {
            totalOPAs += subdep.OPA?.length || 0;
          });
        });
        console.log(`   📊 OPAs encontrados: ${totalOPAs}`);
        if (totalOPAs > 0) {
          const firstDep = Object.values(dependencias)[0] as any;
          console.log(`   📝 Ejemplo dependencia: ${firstDep.nombre?.substring(0, 50)}...`);
        }
      }
    } catch (error) {
      console.log(`   ❌ Error al parsear JSON: ${error}`);
    }
  } else {
    console.log(`❌ ${fileName} - No encontrado`);
  }
}

// =====================================================
// VERIFICAR CONFIGURACIÓN
// =====================================================

console.log('\n🔧 Verificando configuración...');

const envFile = path.join(process.cwd(), '.env.local');
if (fs.existsSync(envFile)) {
  console.log('✅ .env.local encontrado');
  
  const envContent = fs.readFileSync(envFile, 'utf-8');
  const hasSupabaseUrl = envContent.includes('NEXT_PUBLIC_SUPABASE_URL');
  const hasAnonKey = envContent.includes('NEXT_PUBLIC_SUPABASE_ANON_KEY');
  const hasServiceKey = envContent.includes('SUPABASE_SERVICE_ROLE_KEY') && !envContent.includes('# SUPABASE_SERVICE_ROLE_KEY');
  
  console.log(`   📡 Supabase URL: ${hasSupabaseUrl ? '✅' : '❌'}`);
  console.log(`   🔑 Anon Key: ${hasAnonKey ? '✅' : '❌'}`);
  console.log(`   🔐 Service Key: ${hasServiceKey ? '✅' : '⚠️  Comentada'}`);
} else {
  console.log('❌ .env.local no encontrado');
}

// =====================================================
// VERIFICAR DEPENDENCIAS
// =====================================================

console.log('\n📦 Verificando dependencias...');

const packageJsonPath = path.join(process.cwd(), 'package.json');
if (fs.existsSync(packageJsonPath)) {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf-8'));
  
  const requiredDeps = [
    '@supabase/supabase-js',
    'uuid',
    'dotenv',
    'tsx'
  ];
  
  for (const dep of requiredDeps) {
    const hasDevDep = packageJson.devDependencies?.[dep];
    const hasDep = packageJson.dependencies?.[dep];
    
    if (hasDevDep || hasDep) {
      console.log(`✅ ${dep} - ${hasDevDep || hasDep}`);
    } else {
      console.log(`❌ ${dep} - No instalado`);
    }
  }
}

// =====================================================
// RESUMEN
// =====================================================

console.log('\n📋 Resumen de la prueba:');
console.log('✅ Archivos de datos verificados');
console.log('✅ Configuración verificada');
console.log('✅ Dependencias verificadas');
console.log('\n🎯 Próximo paso: Ejecutar ingesta completa');
console.log('💡 Comando: npm run ingestion:all');

console.log('\n🏗️ Winston: Prueba completada exitosamente');
