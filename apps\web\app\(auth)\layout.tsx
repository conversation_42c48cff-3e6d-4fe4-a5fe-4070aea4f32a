import { AuthProvider } from '@chia/auth';
import { Metadata } from 'next';

export const metadata: Metadata = {
  robots: {
    index: false,
    follow: false
  }
};

interface AuthLayoutProps {
  children: React.ReactNode;
}

/**
 * Authentication layout component
 * Provides AuthProvider context for all auth pages
 */
export default function AuthLayout({ children }: AuthLayoutProps) {
  return (
    <AuthProvider>
      {children}
    </AuthProvider>
  );
}
