/**
 * Transformador de datos para el sistema de ingesta CHIA
 * Arquitecto: Winston 🏗️
 * Fecha: 2025-01-07
 */

import { v4 as uuidv4 } from 'uuid';
import {
  Dependencia,
  Subdependencia,
  FAQ,
  Tramite,
  OPA,
  FAQSourceData,
  TramiteSourceData,
  OPASourceData,
  TransformationRule,
  DataMapper
} from './types';

// =====================================================
// TRANSFORMADOR PRINCIPAL
// =====================================================

export class DataTransformer {
  private dependenciaMap: Map<string, string> = new Map(); // codigo -> id
  private subdependenciaMap: Map<string, string> = new Map(); // codigo_dep:codigo_sub -> id

  /**
   * Transforma datos de FAQs desde el formato fuente
   */
  transformFAQData(sourceData: { faqs: FAQSourceData[] }): {
    dependencias: Dependencia[];
    subdependencias: Subdependencia[];
    faqs: FAQ[];
  } {
    const dependenciasMap = new Map<string, Dependencia>();
    const subdependenciasMap = new Map<string, Subdependencia>();
    const faqs: FAQ[] = [];

    sourceData.faqs.forEach(faqGroup => {
      // Procesar dependencia
      const depId = this.getOrCreateDependencia(
        dependenciasMap,
        faqGroup.codigo_dependencia,
        faqGroup.dependencia
      );

      // Procesar subdependencia si existe
      let subDepId: string | undefined;
      if (faqGroup.subdependencia && faqGroup.codigo_subdependencia) {
        subDepId = this.getOrCreateSubdependencia(
          subdependenciasMap,
          faqGroup.codigo_subdependencia,
          faqGroup.subdependencia,
          depId
        );
      }

      // Procesar FAQs
      faqGroup.temas.forEach(tema => {
        tema.preguntas_frecuentes.forEach(pregunta => {
          const faq: FAQ = {
            id: uuidv4(),
            dependencia_id: depId,
            subdependencia_id: subDepId,
            tema: this.cleanText(tema.tema),
            descripcion: this.cleanText(tema.descripcion),
            pregunta: this.cleanText(pregunta.pregunta),
            respuesta: this.cleanText(pregunta.respuesta),
            palabras_clave: this.cleanKeywords(pregunta.palabras_clave),
            prioridad: this.calculatePriority(pregunta.pregunta, pregunta.respuesta),
            activo: true,
            vistas: 0,
            utilidad_promedio: 0.0,
            fuente_original: 'ingesta_inicial',
            version: 1,
            metadata: {
              tema_original: tema.tema,
              codigo_dependencia: faqGroup.codigo_dependencia,
              codigo_subdependencia: faqGroup.codigo_subdependencia
            }
          };
          faqs.push(faq);
        });
      });
    });

    return {
      dependencias: Array.from(dependenciasMap.values()),
      subdependencias: Array.from(subdependenciasMap.values()),
      faqs
    };
  }

  /**
   * Transforma datos de trámites desde el formato fuente
   */
  transformTramiteData(sourceData: TramiteSourceData[]): {
    dependencias: Dependencia[];
    subdependencias: Subdependencia[];
    tramites: Tramite[];
  } {
    const dependenciasMap = new Map<string, Dependencia>();
    const subdependenciasMap = new Map<string, Subdependencia>();
    const tramites: Tramite[] = [];

    sourceData.forEach(tramiteSource => {
      // Procesar dependencia
      const depId = this.getOrCreateDependencia(
        dependenciasMap,
        tramiteSource.codigo_dependencia,
        tramiteSource.dependencia
      );

      // Procesar subdependencia si existe
      let subDepId: string | undefined;
      if (tramiteSource.subdependencia && tramiteSource.codigo_subdependencia) {
        subDepId = this.getOrCreateSubdependencia(
          subdependenciasMap,
          tramiteSource.codigo_subdependencia,
          tramiteSource.subdependencia,
          depId
        );
      }

      // Crear trámite
      const tramite: Tramite = {
        id: uuidv4(),
        nombre: this.cleanText(tramiteSource.Nombre),
        formulario: tramiteSource.Formulario !== 'No' ? tramiteSource.Formulario : undefined,
        tiempo_respuesta: this.cleanText(tramiteSource["Tiempo de respuesta"]),
        tiene_pago: this.cleanText(tramiteSource["¿Tiene pago?"]),
        costo_detalle: this.extractCostDetails(tramiteSource["¿Tiene pago?"]),
        url_suit: this.validateUrl(tramiteSource["Visualización trámite en el SUIT"]),
        url_govco: this.validateUrl(tramiteSource["Visualización trámite en GOV.CO"]),
        dependencia_id: depId,
        subdependencia_id: subDepId,
        palabras_clave: this.generateKeywordsFromText(tramiteSource.Nombre),
        categoria: this.categorizeService(tramiteSource.Nombre, tramiteSource.dependencia),
        modalidad: this.determineModality(tramiteSource.Formulario),
        activo: true,
        popularidad: 0,
        satisfaccion_promedio: 0.0,
        fuente_original: 'ingesta_inicial',
        version: 1,
        metadata: {
          codigo_dependencia: tramiteSource.codigo_dependencia,
          codigo_subdependencia: tramiteSource.codigo_subdependencia,
          formulario_original: tramiteSource.Formulario
        }
      };

      tramites.push(tramite);
    });

    return {
      dependencias: Array.from(dependenciasMap.values()),
      subdependencias: Array.from(subdependenciasMap.values()),
      tramites
    };
  }

  /**
   * Transforma datos de OPAs desde el formato fuente
   */
  transformOPAData(sourceData: OPASourceData): {
    dependencias: Dependencia[];
    subdependencias: Subdependencia[];
    opas: OPA[];
  } {
    const dependenciasMap = new Map<string, Dependencia>();
    const subdependenciasMap = new Map<string, Subdependencia>();
    const opas: OPA[] = [];

    Object.entries(sourceData.dependencias).forEach(([codigoDep, dependencia]) => {
      // Procesar dependencia
      const depId = this.getOrCreateDependencia(
        dependenciasMap,
        codigoDep,
        dependencia.nombre,
        dependencia.sigla
      );

      // Procesar subdependencias y OPAs
      if (dependencia.subdependencias) {
        Object.entries(dependencia.subdependencias).forEach(([codigoSub, subdependencia]) => {
          // Procesar subdependencia
          const subDepId = this.getOrCreateSubdependencia(
            subdependenciasMap,
            codigoSub,
            subdependencia.nombre,
            depId,
            subdependencia.sigla
          );

          // Procesar OPAs
          if (subdependencia.OPA) {
            subdependencia.OPA.forEach((opa, index) => {
              const opaRecord: OPA = {
                id: uuidv4(),
                codigo_opa: opa.codigo_OPA,
                descripcion: this.cleanText(opa.OPA),
                dependencia_id: depId,
                subdependencia_id: subDepId,
                categoria: this.categorizeOPA(opa.OPA),
                tipo_procedimiento: this.determineOPAType(opa.OPA),
                nivel_complejidad: this.calculateComplexity(opa.OPA),
                palabras_clave: this.generateKeywordsFromText(opa.OPA),
                activo: true,
                orden_presentacion: index,
                fuente_original: 'ingesta_inicial',
                version: 1,
                metadata: {
                  codigo_dependencia: codigoDep,
                  codigo_subdependencia: codigoSub,
                  dependencia_sigla: dependencia.sigla,
                  subdependencia_sigla: subdependencia.sigla
                }
              };

              opas.push(opaRecord);
            });
          }
        });
      }
    });

    return {
      dependencias: Array.from(dependenciasMap.values()),
      subdependencias: Array.from(subdependenciasMap.values()),
      opas
    };
  }

  // =====================================================
  // MÉTODOS AUXILIARES
  // =====================================================

  private getOrCreateDependencia(
    map: Map<string, Dependencia>,
    codigo: string,
    nombre: string,
    sigla?: string
  ): string {
    if (map.has(codigo)) {
      return map.get(codigo)!.id!;
    }

    const id = uuidv4();
    const dependencia: Dependencia = {
      id,
      codigo,
      nombre: this.cleanText(nombre),
      sigla: sigla ? this.cleanText(sigla) : this.generateSigla(nombre),
      activo: true,
      metadata: {
        fuente: 'ingesta_inicial'
      }
    };

    map.set(codigo, dependencia);
    this.dependenciaMap.set(codigo, id);
    return id;
  }

  private getOrCreateSubdependencia(
    map: Map<string, Subdependencia>,
    codigo: string,
    nombre: string,
    dependenciaId: string,
    sigla?: string
  ): string {
    const key = `${dependenciaId}:${codigo}`;
    if (map.has(key)) {
      return map.get(key)!.id!;
    }

    const id = uuidv4();
    const subdependencia: Subdependencia = {
      id,
      codigo,
      nombre: this.cleanText(nombre),
      sigla: sigla ? this.cleanText(sigla) : this.generateSigla(nombre),
      dependencia_id: dependenciaId,
      activo: true,
      metadata: {
        fuente: 'ingesta_inicial'
      }
    };

    map.set(key, subdependencia);
    this.subdependenciaMap.set(key, id);
    return id;
  }

  private cleanText(text: string | undefined): string {
    if (!text) return '';
    return text
      .trim()
      .replace(/\s+/g, ' ')
      .replace(/[""]/g, '"')
      .replace(/['']/g, "'");
  }

  private cleanKeywords(keywords: string[]): string[] {
    return keywords
      .map(k => this.cleanText(k))
      .filter(k => k.length > 0)
      .map(k => k.toLowerCase());
  }

  private generateKeywordsFromText(text: string): string[] {
    const stopWords = new Set([
      'el', 'la', 'de', 'que', 'y', 'a', 'en', 'un', 'es', 'se', 'no', 'te', 'lo', 'le',
      'da', 'su', 'por', 'son', 'con', 'para', 'al', 'del', 'los', 'las', 'una', 'como'
    ]);

    return this.cleanText(text)
      .toLowerCase()
      .split(/\s+/)
      .filter(word => word.length > 2 && !stopWords.has(word))
      .slice(0, 10); // Máximo 10 palabras clave
  }

  private generateSigla(nombre: string): string {
    return nombre
      .split(' ')
      .map(word => word.charAt(0).toUpperCase())
      .join('')
      .substring(0, 10);
  }

  private validateUrl(url: string): string | undefined {
    if (!url || url === 'No' || url === '') return undefined;
    try {
      new URL(url);
      return url;
    } catch {
      return undefined;
    }
  }

  private extractCostDetails(pagoInfo: string): string | undefined {
    if (!pagoInfo || pagoInfo === 'No') return undefined;
    return this.cleanText(pagoInfo);
  }

  private categorizeService(nombre: string, dependencia: string): string {
    const nombreLower = nombre.toLowerCase();
    const depLower = dependencia.toLowerCase();

    if (nombreLower.includes('impuesto') || nombreLower.includes('tributario')) {
      return 'tributario';
    }
    if (nombreLower.includes('certificado') || nombreLower.includes('certificación')) {
      return 'certificacion';
    }
    if (nombreLower.includes('licencia') || nombreLower.includes('permiso')) {
      return 'licencias_permisos';
    }
    if (depLower.includes('salud')) {
      return 'salud';
    }
    if (depLower.includes('planeación') || depLower.includes('urbanismo')) {
      return 'urbanismo';
    }
    
    return 'general';
  }

  private determineModality(formulario: string): string[] {
    if (!formulario || formulario === 'No') {
      return ['presencial'];
    }
    
    const modalidades: string[] = [];
    if (formulario.toLowerCase().includes('línea') || formulario.toLowerCase().includes('virtual')) {
      modalidades.push('virtual');
    }
    if (formulario.toLowerCase().includes('presencial')) {
      modalidades.push('presencial');
    }
    
    return modalidades.length > 0 ? modalidades : ['presencial'];
  }

  private categorizeOPA(descripcion: string): string {
    const desc = descripcion.toLowerCase();
    
    if (desc.includes('solicitud') || desc.includes('petición')) {
      return 'solicitudes';
    }
    if (desc.includes('certificado') || desc.includes('certificación')) {
      return 'certificaciones';
    }
    if (desc.includes('informe') || desc.includes('reporte')) {
      return 'informes';
    }
    if (desc.includes('registro') || desc.includes('inscripción')) {
      return 'registros';
    }
    
    return 'procedimientos_generales';
  }

  private determineOPAType(descripcion: string): string {
    const desc = descripcion.toLowerCase();
    
    if (desc.includes('trámite')) {
      return 'tramite';
    }
    if (desc.includes('consulta') || desc.includes('información')) {
      return 'consulta';
    }
    if (desc.includes('autorización') || desc.includes('permiso')) {
      return 'autorizacion';
    }
    
    return 'procedimiento';
  }

  private calculateComplexity(descripcion: string): number {
    const length = descripcion.length;
    const words = descripcion.split(' ').length;
    
    if (length < 100 && words < 20) return 1;
    if (length < 200 && words < 40) return 2;
    if (length < 400 && words < 80) return 3;
    if (length < 600 && words < 120) return 4;
    return 5;
  }

  private calculatePriority(pregunta: string, respuesta: string): number {
    let priority = 0;
    
    // Preguntas más comunes tienen mayor prioridad
    const commonQuestions = ['qué', 'cómo', 'cuándo', 'dónde', 'cuánto', 'requisitos'];
    const preguntaLower = pregunta.toLowerCase();
    
    for (const common of commonQuestions) {
      if (preguntaLower.includes(common)) {
        priority += 2;
      }
    }
    
    // Respuestas más detalladas tienen mayor prioridad
    if (respuesta.length > 200) priority += 1;
    if (respuesta.length > 500) priority += 1;
    
    return Math.min(priority, 10);
  }
}
