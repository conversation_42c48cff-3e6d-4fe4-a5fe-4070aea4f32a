import { Metadata } from 'next';
import Link from 'next/link';
import PageLayout from '@/components/layout/PageLayout';
import {
  CreditCardIcon,
  BanknotesIcon,
  ShieldCheckIcon,
  ClockIcon,
  ArrowRightIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

export const metadata: Metadata = {
  title: 'Pagos en Línea | Portal CHIA',
  description: 'Paga tus impuestos municipales, multas y tasas de forma segura y rápida desde el Portal CHIA.',
  keywords: 'pagos en línea, impuestos municipales, multas, tasas, PSE, tarjeta crédito, Chía',
};

const paymentTypes = [
  {
    id: 'predial',
    name: 'Impuesto Predial',
    description: 'Pago del impuesto predial unificado anual',
    icon: BanknotesIcon,
    features: ['Descuentos por pronto pago', 'Fraccionamiento disponible', 'Consulta de avalúo'],
    popular: true
  },
  {
    id: 'industria',
    name: 'Impuesto de Industria y Comercio',
    description: 'ICA para establecimientos comerciales e industriales',
    icon: CreditCardIcon,
    features: ['Declaración en línea', 'Cálculo automático', 'Historial de pagos'],
    popular: true
  },
  {
    id: 'multas',
    name: 'Multas de Tránsito',
    description: 'Pago de comparendos y multas de tránsito',
    icon: ShieldCheckIcon,
    features: ['Descuentos por pronto pago', 'Consulta por placa', 'Certificado de paz y salvo'],
    popular: false
  },
  {
    id: 'servicios',
    name: 'Tasas y Servicios',
    description: 'Derechos de petición, licencias y otros servicios',
    icon: BanknotesIcon,
    features: ['Múltiples servicios', 'Tarifas actualizadas', 'Recibo inmediato'],
    popular: false
  }
];

const paymentMethods = [
  {
    name: 'PSE',
    description: 'Pagos Seguros en Línea',
    logo: '🏦',
    fee: 'Sin costo adicional'
  },
  {
    name: 'Tarjeta de Crédito',
    description: 'Visa, Mastercard, American Express',
    logo: '💳',
    fee: '2.5% + IVA'
  },
  {
    name: 'Tarjeta Débito',
    description: 'Débito de cuentas corrientes y ahorros',
    logo: '💳',
    fee: '1.5% + IVA'
  },
  {
    name: 'Corresponsales Bancarios',
    description: 'Efecty, Baloto, Paga Todo',
    logo: '🏪',
    fee: 'Según corresponsal'
  }
];

export default function PagosPage() {
  return (
    <PageLayout className="bg-gray-50">
      {/* Breadcrumb */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-4">
              <li>
                <Link href="/" className="text-gray-400 hover:text-gray-500">
                  Inicio
                </Link>
              </li>
              <li>
                <ArrowRightIcon className="h-4 w-4 text-gray-400" />
              </li>
              <li>
                <Link href="/servicios" className="text-gray-400 hover:text-gray-500">
                  Servicios
                </Link>
              </li>
              <li>
                <ArrowRightIcon className="h-4 w-4 text-gray-400" />
              </li>
              <li>
                <span className="text-gray-900 font-medium">Pagos en Línea</span>
              </li>
            </ol>
          </nav>
        </div>
      </div>

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-green-600 to-green-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <CreditCardIcon className="h-16 w-16 mx-auto mb-4 text-green-200" />
            <h1 className="text-4xl font-bold mb-4">
              Pagos en Línea
            </h1>
            <p className="text-xl text-green-100 mb-8 max-w-3xl mx-auto">
              Paga tus obligaciones municipales de forma rápida, segura y desde cualquier lugar. 
              Múltiples métodos de pago disponibles las 24 horas del día.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Security Banner */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
          <div className="flex items-center gap-3 mb-4">
            <ShieldCheckIcon className="h-6 w-6 text-green-600" />
            <h3 className="font-semibold text-green-900">
              Pagos 100% Seguros
            </h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-green-800 text-sm">
            <div className="flex items-center gap-2">
              <CheckCircleIcon className="h-4 w-4 text-green-600" />
              Encriptación SSL de 256 bits
            </div>
            <div className="flex items-center gap-2">
              <CheckCircleIcon className="h-4 w-4 text-green-600" />
              Certificación PCI DSS
            </div>
            <div className="flex items-center gap-2">
              <CheckCircleIcon className="h-4 w-4 text-green-600" />
              Respaldo de entidades financieras
            </div>
          </div>
        </div>

        {/* Payment Types */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {paymentTypes.map((type) => (
            <div key={type.id} className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 hover:shadow-lg transition-shadow">
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="p-3 bg-green-100 rounded-lg">
                      <type.icon className="h-6 w-6 text-green-600" />
                    </div>
                    {type.popular && (
                      <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">
                        Popular
                      </span>
                    )}
                  </div>
                </div>
                
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {type.name}
                </h3>
                
                <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                  {type.description}
                </p>
                
                <div className="mb-6">
                  <h4 className="font-medium text-gray-900 mb-3">Características:</h4>
                  <ul className="space-y-2">
                    {type.features.map((feature, index) => (
                      <li key={index} className="flex items-start gap-2 text-sm text-gray-600">
                        <CheckCircleIcon className="h-4 w-4 text-green-500 flex-shrink-0 mt-0.5" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
              
              <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
                <button className="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                  Realizar Pago
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Payment Methods */}
        <div className="bg-white rounded-xl shadow-md p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            Métodos de Pago Disponibles
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {paymentMethods.map((method, index) => (
              <div key={index} className="text-center p-4 border border-gray-200 rounded-lg hover:border-green-300 transition-colors">
                <div className="text-4xl mb-3">{method.logo}</div>
                <h3 className="font-semibold text-gray-900 mb-2">{method.name}</h3>
                <p className="text-gray-600 text-sm mb-2">{method.description}</p>
                <p className="text-green-600 text-xs font-medium">{method.fee}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Process Steps */}
        <div className="bg-white rounded-xl shadow-md p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            ¿Cómo realizar tu pago?
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-green-600 font-bold text-lg">1</span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Consulta</h3>
              <p className="text-gray-600 text-sm">Ingresa tu número de documento o código de referencia</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-green-600 font-bold text-lg">2</span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Verifica</h3>
              <p className="text-gray-600 text-sm">Revisa el detalle de tu obligación y el monto a pagar</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-green-600 font-bold text-lg">3</span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Paga</h3>
              <p className="text-gray-600 text-sm">Selecciona tu método de pago preferido</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-green-600 font-bold text-lg">4</span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Confirma</h3>
              <p className="text-gray-600 text-sm">Recibe tu comprobante de pago por email</p>
            </div>
          </div>
        </div>

        {/* Quick Access */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
          <div className="bg-white rounded-xl shadow-md p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              Consulta Rápida
            </h3>
            <p className="text-gray-600 mb-4">
              Consulta el estado de tus obligaciones municipales
            </p>
            <div className="space-y-3">
              <input
                type="text"
                placeholder="Número de documento"
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
              <button className="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                Consultar
              </button>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-md p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              Pago con Referencia
            </h3>
            <p className="text-gray-600 mb-4">
              ¿Ya tienes el código de referencia de tu factura?
            </p>
            <div className="space-y-3">
              <input
                type="text"
                placeholder="Código de referencia"
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
              <button className="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                Pagar Ahora
              </button>
            </div>
          </div>
        </div>

        {/* Help Section */}
        <div className="bg-white rounded-xl shadow-md p-8 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            ¿Problemas con tu pago?
          </h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Nuestro equipo de soporte está disponible para ayudarte con cualquier 
            inconveniente en el proceso de pago.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/chat"
              className="inline-flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
            >
              Ayuda Inmediata
            </Link>
            <Link
              href="/contacto"
              className="inline-flex items-center gap-2 bg-white hover:bg-gray-50 text-green-600 border border-green-600 px-6 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
            >
              Contactar Soporte
            </Link>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
