import { getSupabaseClient, getSupabaseAdminClient } from '../lib/supabase-client';
import type { SupabaseClient } from '@supabase/supabase-js';
import type {
  LoginCredentials,
  RegistrationData,
  PasswordResetData,
  UserProfile,
  AuthSession,
  TwoFactorSetup,
  TwoFactorVerification,
  AuthError
} from '../types/auth';
import { UserRole, TwoFactorStatus } from '../types/auth';
import { AuthError as SupabaseAuthError } from '@supabase/supabase-js';

/**
 * Authentication service class
 * Handles all authentication operations with Supabase
 */
export class AuthService {
  private supabase = getSupabaseClient();
  private adminSupabase: SupabaseClient | null = null;

  /**
   * Get admin Supabase client (lazy initialization)
   */
  private getAdminClient(): SupabaseClient {
    if (!this.adminSupabase) {
      try {
        this.adminSupabase = getSupabaseAdminClient();
      } catch (error) {
        console.warn('Admin client not available, using regular client:', error);
        this.adminSupabase = this.supabase;
      }
    }
    return this.adminSupabase;
  }

  /**
   * Register a new citizen user
   */
  async register(data: RegistrationData): Promise<UserProfile> {
    try {
      // Step 1: Create auth user with Supabase Auth
      const { data: authData, error: authError } = await this.supabase.auth.signUp({
        email: data.email,
        password: data.password,
        options: {
          data: {
            first_name: data.firstName,
            last_name: data.lastName,
            document_type: data.documentType,
            document_number: data.documentNumber,
            phone: data.phone,
            city: data.city,
            department: data.department,
            birth_date: data.birthDate,
            role: UserRole.CIUDADANO
          }
        }
      });

      if (authError) {
        throw this.mapSupabaseError(authError);
      }

      if (!authData.user) {
        throw new Error('Failed to create user account');
      }

      // Step 2: Create citizen profile in database
      const { data: profileData, error: profileError } = await this.supabase
        .from('ciudadanos')
        .insert({
          id: authData.user.id,
          email: data.email,
          nombre: data.firstName,
          apellido: data.lastName,
          tipo_documento: data.documentType,
          documento_identidad: data.documentNumber,
          telefono: data.phone,
          direccion: `${data.city}, ${data.department}`,
          fecha_nacimiento: data.birthDate
        })
        .select()
        .single();

      if (profileError) {
        // Cleanup auth user if profile creation fails
        try {
          await this.getAdminClient().auth.admin.deleteUser(authData.user.id);
        } catch (adminError) {
          console.warn('Could not cleanup user after profile creation failure:', adminError);
        }
        throw this.mapDatabaseError(profileError);
      }

      // Step 3: Log registration event
      await this.logAuditEvent(authData.user.id, 'user_registered', 'user', authData.user.id);

      return this.mapDatabaseUserToProfile(profileData);
    } catch (error) {
      throw this.handleError(error, 'Registration failed');
    }
  }

  /**
   * Login user with email and password
   */
  async login(credentials: LoginCredentials): Promise<AuthSession> {
    try {
      // Step 1: Authenticate with Supabase Auth
      const { data: authData, error: authError } = await this.supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password
      });

      if (authError) {
        throw this.mapSupabaseError(authError);
      }

      if (!authData.user || !authData.session) {
        throw new Error('Authentication failed');
      }

      // Step 2: Get user profile
      const profile = await this.getUserProfile(authData.user.id);

      // Step 3: Check if 2FA is required
      if (profile.twoFactorEnabled && !credentials.twoFactorCode) {
        throw new Error('Two-factor authentication code required');
      }

      // Step 4: Verify 2FA code if provided
      if (profile.twoFactorEnabled && credentials.twoFactorCode) {
        await this.verifyTwoFactorCode(authData.user.id, credentials.twoFactorCode);
      }

      // Step 5: Update last login timestamp
      await this.supabase
        .from('ciudadano')
        .update({ last_login_at: new Date().toISOString() })
        .eq('id', authData.user.id);

      // Step 6: Create session record
      const sessionData = await this.createSessionRecord(authData.user.id, authData.session);

      // Step 7: Log login event
      await this.logAuditEvent(authData.user.id, 'user_login', 'user', authData.user.id);

      return {
        user: profile,
        accessToken: authData.session.access_token,
        refreshToken: authData.session.refresh_token,
        expiresAt: authData.session.expires_at || 0,
        sessionId: sessionData.id
      };
    } catch (error) {
      throw this.handleError(error, 'Login failed');
    }
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    try {
      const { data: { user } } = await this.supabase.auth.getUser();
      
      if (user) {
        // Deactivate session records
        await this.supabase
          .from('user_sessions')
          .update({ is_active: false })
          .eq('user_id', user.id);

        // Log logout event
        await this.logAuditEvent(user.id, 'user_logout', 'user', user.id);
      }

      // Sign out from Supabase Auth
      const { error } = await this.supabase.auth.signOut();
      if (error) {
        throw this.mapSupabaseError(error);
      }
    } catch (error) {
      throw this.handleError(error, 'Logout failed');
    }
  }

  /**
   * Get current user profile
   */
  async getCurrentUser(): Promise<UserProfile | null> {
    try {
      const { data: { user }, error } = await this.supabase.auth.getUser();

      if (error) {
        console.warn('Auth error in getCurrentUser:', error);
        return null;
      }

      if (!user) {
        return null;
      }

      const profile = await this.getUserProfile(user.id);
      return profile;
    } catch (error) {
      console.error('Failed to get current user:', error);
      // Don't throw error, just return null to indicate no authenticated user
      return null;
    }
  }

  /**
   * Refresh authentication session
   */
  async refreshSession(): Promise<AuthSession | null> {
    try {
      const { data, error } = await this.supabase.auth.refreshSession();
      
      if (error) {
        throw this.mapSupabaseError(error);
      }

      if (!data.session || !data.user) {
        return null;
      }

      const profile = await this.getUserProfile(data.user.id);

      return {
        user: profile,
        accessToken: data.session.access_token,
        refreshToken: data.session.refresh_token,
        expiresAt: data.session.expires_at || 0,
        sessionId: '' // Will be updated by session management
      };
    } catch (error) {
      console.error('Failed to refresh session:', error);
      return null;
    }
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(email: string): Promise<void> {
    try {
      const { error } = await this.supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`
      });

      if (error) {
        throw this.mapSupabaseError(error);
      }

      // Log password reset request
      const { data: userData } = await this.supabase
        .from('ciudadano')
        .select('id')
        .eq('email', email)
        .single();

      if (userData) {
        await this.logAuditEvent(userData.id, 'password_reset_requested', 'user', userData.id);
      }
    } catch (error) {
      throw this.handleError(error, 'Password reset request failed');
    }
  }

  /**
   * Update password with reset token
   */
  async updatePassword(data: PasswordResetData): Promise<void> {
    try {
      const { error } = await this.supabase.auth.updateUser({
        password: data.newPassword
      });

      if (error) {
        throw this.mapSupabaseError(error);
      }

      // Log password update
      const { data: { user } } = await this.supabase.auth.getUser();
      if (user) {
        await this.logAuditEvent(user.id, 'password_updated', 'user', user.id);
      }
    } catch (error) {
      throw this.handleError(error, 'Password update failed');
    }
  }

  /**
   * Update user profile information
   */
  async updateProfile(profileData: Partial<UserProfile>): Promise<UserProfile> {
    try {
      const { data: { user }, error: authError } = await this.supabase.auth.getUser();

      if (authError || !user) {
        throw new Error('User not authenticated');
      }

      // Update ciudadanos table
      const { error: updateError } = await this.supabase
        .from('ciudadanos')
        .update({
          nombre: profileData.firstName,
          apellido: profileData.lastName,
          telefono: profileData.phone,
          direccion: profileData.address,
          updated_at: new Date().toISOString()
        })
        .eq('auth_id', user.id);

      if (updateError) {
        throw this.mapDatabaseError(updateError);
      }

      // Update auth user metadata if needed
      if (profileData.city || profileData.department) {
        const { error: metadataError } = await this.supabase.auth.updateUser({
          data: {
            ...user.user_metadata,
            city: profileData.city,
            department: profileData.department
          }
        });

        if (metadataError) {
          throw this.mapSupabaseError(metadataError);
        }
      }

      // Log audit event
      await this.logAuditEvent(user.id, 'profile_updated', 'ciudadano', user.id, profileData);

      // Return updated profile
      return await this.getUserProfile(user.id);
    } catch (error) {
      throw this.handleError(error, 'Profile update failed');
    }
  }

  /**
   * Get user profile by ID
   */
  private async getUserProfile(userId: string): Promise<UserProfile> {
    try {
      // First, try to get profile using the database function
      const { data: profileData, error } = await this.supabase
        .rpc('get_user_profile', { user_uuid: userId });

      if (error) {
        console.warn('Error calling get_user_profile function:', error);
        // Fallback to direct table query
        return await this.getUserProfileFallback(userId);
      }

      if (!profileData) {
        console.warn('No profile data returned from function, trying fallback');
        // Fallback to direct table query
        return await this.getUserProfileFallback(userId);
      }

      // Get auth user data for additional info
      const { data: { user }, error: authError } = await this.supabase.auth.getUser();

      return {
        id: profileData.id,
        authId: profileData.auth_id,
        email: profileData.email,
        firstName: profileData.nombre || '',
        lastName: profileData.apellido || '',
        documentType: profileData.tipo_documento || 'CC',
        documentNumber: profileData.documento_identidad || '',
        phone: profileData.telefono || '',
        address: profileData.direccion || '',
        city: user?.user_metadata?.city || '',
        department: user?.user_metadata?.department || '',
        birthDate: profileData.fecha_nacimiento || '',
        role: profileData.role || 'ciudadano',
        permissions: profileData.permissions || [],
        emailConfirmed: user?.email_confirmed_at !== null,
        phoneConfirmed: user?.phone_confirmed_at !== null,
        twoFactorEnabled: false, // Will be implemented in 2FA task
        lastLoginAt: user?.last_sign_in_at || '',
        createdAt: profileData.created_at,
        updatedAt: profileData.updated_at
      };
    } catch (error) {
      console.warn('Error in getUserProfile, trying fallback:', error);
      return await this.getUserProfileFallback(userId);
    }
  }

  /**
   * Fallback method to get user profile directly from tables
   */
  private async getUserProfileFallback(userId: string): Promise<UserProfile> {
    // Get auth user data first
    const { data: { user }, error: authError } = await this.supabase.auth.getUser();

    if (authError || !user) {
      throw new Error('User not authenticated');
    }

    // Get ciudadano data
    const { data: ciudadano, error: ciudadanoError } = await this.supabase
      .from('ciudadanos')
      .select('*')
      .eq('auth_id', userId)
      .single();

    // Get user role
    const { data: userRole } = await this.supabase
      .from('user_roles')
      .select('role, permissions')
      .eq('user_id', userId)
      .single();

    // If no ciudadano record exists, create a basic profile from auth data
    if (ciudadanoError || !ciudadano) {
      console.warn('No ciudadano record found, creating basic profile from auth data');

      return {
        id: userId,
        authId: userId,
        email: user.email || '',
        firstName: user.user_metadata?.first_name || '',
        lastName: user.user_metadata?.last_name || '',
        documentType: 'CC',
        documentNumber: user.user_metadata?.document_number || '',
        phone: user.phone || '',
        address: user.user_metadata?.address || '',
        city: user.user_metadata?.city || '',
        department: user.user_metadata?.department || '',
        birthDate: user.user_metadata?.birth_date || '',
        role: userRole?.role || 'ciudadano',
        permissions: userRole?.permissions || [],
        emailConfirmed: user.email_confirmed_at !== null,
        phoneConfirmed: user.phone_confirmed_at !== null,
        twoFactorEnabled: false,
        lastLoginAt: user.last_sign_in_at || '',
        createdAt: user.created_at,
        updatedAt: user.updated_at || user.created_at
      };
    }

    return {
      id: ciudadano.id,
      authId: ciudadano.auth_id,
      email: ciudadano.email,
      firstName: ciudadano.nombre || '',
      lastName: ciudadano.apellido || '',
      documentType: ciudadano.tipo_documento || 'CC',
      documentNumber: ciudadano.documento_identidad || '',
      phone: ciudadano.telefono || '',
      address: ciudadano.direccion || '',
      city: user.user_metadata?.city || '',
      department: user.user_metadata?.department || '',
      birthDate: ciudadano.fecha_nacimiento || '',
      role: userRole?.role || 'ciudadano',
      permissions: userRole?.permissions || [],
      emailConfirmed: user.email_confirmed_at !== null,
      phoneConfirmed: user.phone_confirmed_at !== null,
      twoFactorEnabled: false,
      lastLoginAt: user.last_sign_in_at || '',
      createdAt: ciudadano.created_at,
      updatedAt: ciudadano.updated_at
    };
  }

  /**
   * Create session record in database
   */
  private async createSessionRecord(userId: string, session: any): Promise<any> {
    const { data, error } = await this.supabase
      .from('user_sessions')
      .insert({
        user_id: userId,
        session_token: session.access_token,
        refresh_token: session.refresh_token,
        expires_at: new Date(session.expires_at * 1000).toISOString(),
        is_active: true
      })
      .select()
      .single();

    if (error) {
      throw this.mapDatabaseError(error);
    }

    return data;
  }

  /**
   * Verify two-factor authentication code
   */
  private async verifyTwoFactorCode(userId: string, code: string): Promise<void> {
    // Implementation will be added in the 2FA service
    throw new Error('Two-factor authentication not yet implemented');
  }

  /**
   * Log audit event
   */
  private async logAuditEvent(
    userId: string | null,
    action: string,
    resourceType: string,
    resourceId: string | null,
    details?: Record<string, any>
  ): Promise<void> {
    try {
      await this.supabase
        .from('audit_log')
        .insert({
          user_id: userId,
          action,
          resource_type: resourceType,
          resource_id: resourceId,
          details,
          created_at: new Date().toISOString()
        });
    } catch (error) {
      console.error('Failed to log audit event:', error);
    }
  }

  /**
   * Map database user to UserProfile interface
   */
  private mapDatabaseUserToProfile(dbUser: any): UserProfile {
    return {
      id: dbUser.id,
      authId: dbUser.auth_id,
      email: dbUser.email,
      role: UserRole.CIUDADANO,
      firstName: dbUser.nombre,
      lastName: dbUser.apellido,
      documentType: dbUser.tipo_documento,
      documentNumber: dbUser.documento_identidad,
      phone: dbUser.telefono,
      address: dbUser.direccion,
      city: dbUser.direccion ? dbUser.direccion.split(', ')[0] : '',
      department: dbUser.direccion ? dbUser.direccion.split(', ')[1] : '',
      birthDate: dbUser.fecha_nacimiento,
      emailConfirmed: false, // Will be updated when email verification is implemented
      phoneConfirmed: false, // Will be updated when phone verification is implemented
      twoFactorEnabled: false, // Will be updated when 2FA is implemented
      permissions: [],
      lastLoginAt: undefined, // Will be updated when login tracking is implemented
      createdAt: dbUser.created_at,
      updatedAt: dbUser.updated_at,
      metadata: {}
    };
  }

  /**
   * Map Supabase auth errors to AuthError
   */
  private mapSupabaseError(error: SupabaseAuthError): AuthError {
    return {
      code: error.name || 'AUTH_ERROR',
      message: error.message,
      details: { originalError: error }
    };
  }

  /**
   * Map database errors to AuthError
   */
  private mapDatabaseError(error: any): AuthError {
    return {
      code: error.code || 'DATABASE_ERROR',
      message: error.message || 'Database operation failed',
      details: { originalError: error }
    };
  }

  /**
   * Handle and format errors
   */
  private handleError(error: any, context: string): AuthError {
    if (error.code && error.message) {
      return error as AuthError;
    }

    return {
      code: 'UNKNOWN_ERROR',
      message: `${context}: ${error.message || 'Unknown error occurred'}`,
      details: { originalError: error }
    };
  }
}
