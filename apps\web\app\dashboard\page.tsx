import { Metadata } from 'next';
import { DashboardClient } from './dashboard-client';

export const metadata: Metadata = {
  title: 'Dashboard | Portal Ciudadano Digital - Chía',
  description: 'Panel de control del ciudadano para gestionar trámites y servicios municipales.',
  robots: {
    index: false, // Don't index protected pages
    follow: false
  }
};

/**
 * Dashboard page component
 * Protected route for authenticated citizens
 */
export default function DashboardPage() {
  return <DashboardClient />;
}
