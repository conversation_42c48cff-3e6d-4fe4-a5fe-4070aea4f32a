import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import * as speakeasy from 'speakeasy';
import * as QRCode from 'qrcode';

/**
 * POST /api/auth/2fa/setup
 * Generate TOTP secret and QR code for 2FA setup
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Usuario no autenticado' },
        { status: 401 }
      );
    }

    // Get user profile
    const { data: profile } = await supabase
      .from('ciudadanos')
      .select('nombre, apellido, email')
      .eq('auth_id', user.id)
      .single();

    if (!profile) {
      return NextResponse.json(
        { error: 'Perfil de usuario no encontrado' },
        { status: 404 }
      );
    }

    // Generate TOTP secret
    const secret = speakeasy.generateSecret({
      name: `${profile.nombre} ${profile.apellido}`,
      issuer: 'Portal Ciudadano Digital - Chía',
      length: 32
    });

    // Generate QR code
    const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url!);

    // Store temporary secret in user metadata (will be moved to permanent storage after verification)
    const { error: updateError } = await supabase.auth.updateUser({
      data: {
        ...user.user_metadata,
        temp_2fa_secret: secret.base32
      }
    });

    if (updateError) {
      console.error('Error storing temporary 2FA secret:', updateError);
      return NextResponse.json(
        { error: 'Error al configurar 2FA' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      secret: secret.base32,
      qrCodeUrl: qrCodeUrl,
      manualEntryKey: secret.base32
    });

  } catch (error) {
    console.error('Error in 2FA setup:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
