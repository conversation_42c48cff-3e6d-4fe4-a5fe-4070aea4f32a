INSERT INTO ingestion.dependencias (codigo, nombre, descripcion, fuente_original) VALUES ('000', '<PERSON><PERSON><PERSON> del alcalde', 'Dependencia: <PERSON><PERSON><PERSON> del alcalde', 'faqs_chia_estructurado') ON CONFLICT DO NOTHING;


        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '000'),
          '005',
          'OFICINA DE TECNOLOGIAS DE LA INFORMACIÓN – TIC',
          'Subdependencia: OFICINA DE TECNOLOGIAS DE LA INFORMACIÓN – TIC',
          'faqs_chia_estructurado'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      

INSERT INTO ingestion.dependencias (codigo, nombre, descripcion, fuente_original) VALUES ('030', 'SECRETARÍA DE GOBIERNO', 'Dependencia: SECRETARÍA DE GOBIERNO', 'faqs_chia_estructurado') ON CONFLICT DO NOTHING;


        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '030'),
          '',
          '',
          'Subdependencia: ',
          'faqs_chia_estructurado'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      


        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '030'),
          '031',
          'DIRECCIÓN DE SEGURIDAD Y CONVIVENCIA CIUDADANA',
          'Subdependencia: DIRECCIÓN DE SEGURIDAD Y CONVIVENCIA CIUDADANA',
          'faqs_chia_estructurado'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      


        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '030'),
          '033',
          'DIRECCIÓN DE DERECHOS Y RESOLUCIÓN DE CONFLICTOS.',
          'Subdependencia: DIRECCIÓN DE DERECHOS Y RESOLUCIÓN DE CONFLICTOS.',
          'faqs_chia_estructurado'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      

INSERT INTO ingestion.dependencias (codigo, nombre, descripcion, fuente_original) VALUES ('040', 'SECRETARIA DE HACIENDA', 'Dependencia: SECRETARIA DE HACIENDA', 'faqs_chia_estructurado') ON CONFLICT DO NOTHING;


        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '040'),
          '042',
          'DIRECCIÓN FINANCIERA',
          'Subdependencia: DIRECCIÓN FINANCIERA',
          'faqs_chia_estructurado'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      

INSERT INTO ingestion.dependencias (codigo, nombre, descripcion, fuente_original) VALUES ('060', 'SECRETARIA DE DESARROLLO SOCIAL', 'Dependencia: SECRETARIA DE DESARROLLO SOCIAL', 'faqs_chia_estructurado') ON CONFLICT DO NOTHING;


        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '060'),
          '062',
          'DIRECCIÓN DE ACCIÓN SOCIAL',
          'Subdependencia: DIRECCIÓN DE ACCIÓN SOCIAL',
          'faqs_chia_estructurado'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      


        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '060'),
          '061',
          'DIRECCIÓN CIUDADANIA JUVENIL',
          'Subdependencia: DIRECCIÓN CIUDADANIA JUVENIL',
          'faqs_chia_estructurado'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      


        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '060'),
          '063',
          'DIRECCIÓN CULTURA',
          'Subdependencia: DIRECCIÓN CULTURA',
          'faqs_chia_estructurado'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      

INSERT INTO ingestion.dependencias (codigo, nombre, descripcion, fuente_original) VALUES ('070', 'SECRETARÍA DE EDUCACIÓN', 'Dependencia: SECRETARÍA DE EDUCACIÓN', 'faqs_chia_estructurado') ON CONFLICT DO NOTHING;


        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '070'),
          '',
          '',
          'Subdependencia: ',
          'faqs_chia_estructurado'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      

INSERT INTO ingestion.dependencias (codigo, nombre, descripcion, fuente_original) VALUES ('080', 'SECRETARIA DE SALUD', 'Dependencia: SECRETARIA DE SALUD', 'faqs_chia_estructurado') ON CONFLICT DO NOTHING;


        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '080'),
          '',
          '',
          'Subdependencia: ',
          'faqs_chia_estructurado'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      

INSERT INTO ingestion.dependencias (codigo, nombre, descripcion, fuente_original) VALUES ('090', 'SECRETARÍA PARA EL DESARROLLO ECONÓMICO', 'Dependencia: SECRETARÍA PARA EL DESARROLLO ECONÓMICO', 'faqs_chia_estructurado') ON CONFLICT DO NOTHING;


        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '090'),
          '091',
          'DIRECCIÓN DE DESARROLLO AGROPECUARIO Y EMPRESARIAL',
          'Subdependencia: DIRECCIÓN DE DESARROLLO AGROPECUARIO Y EMPRESARIAL',
          'faqs_chia_estructurado'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      


        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '090'),
          '092',
          'DIRECCIÓN DE TURISMO',
          'Subdependencia: DIRECCIÓN DE TURISMO',
          'faqs_chia_estructurado'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      

INSERT INTO ingestion.dependencias (codigo, nombre, descripcion, fuente_original) VALUES ('120', 'SECRETARÍA DE PARTICIPACIÓN CIUDADANA Y ACCIÓN COMUNITARIA.', 'Dependencia: SECRETARÍA DE PARTICIPACIÓN CIUDADANA Y ACCIÓN COMUNITARIA.', 'faqs_chia_estructurado') ON CONFLICT DO NOTHING;


        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '120'),
          '',
          '',
          'Subdependencia: ',
          'faqs_chia_estructurado'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      


        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '040'),
          '041',
          'Dirección de Rentas',
          'Subdependencia: Dirección de Rentas',
          'tramites_chia_optimo'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      


        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '000'),
          '001',
          'Oficina Asesora Jurídica',
          'Subdependencia: Oficina Asesora Jurídica',
          'tramites_chia_optimo'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      


        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '070'),
          '073',
          'Dirección Administrativa y Financiera',
          'Subdependencia: Dirección Administrativa y Financiera',
          'tramites_chia_optimo'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      


        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '070'),
          '071',
          'Dirección de Inspección y Vigilancia',
          'Subdependencia: Dirección de Inspección y Vigilancia',
          'tramites_chia_optimo'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      


        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '030'),
          '030',
          'Directo',
          'Subdependencia: Directo',
          'tramites_chia_optimo'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      


        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '030'),
          '032',
          'Dirección de asuntos étnicos, raciales, religiosos y posconflicto.',
          'Subdependencia: Dirección de asuntos étnicos, raciales, religiosos y posconflicto.',
          'tramites_chia_optimo'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      

INSERT INTO ingestion.dependencias (codigo, nombre, descripcion, fuente_original) VALUES ('100', 'Secretaría de Medio Ambiente', 'Dependencia: Secretaría de Medio Ambiente', 'tramites_chia_optimo') ON CONFLICT DO NOTHING;


        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '100'),
          '109',
          'Publicidad',
          'Subdependencia: Publicidad',
          'tramites_chia_optimo'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      

INSERT INTO ingestion.dependencias (codigo, nombre, descripcion, fuente_original) VALUES ('110', 'Secretaría de Movilidad', 'Dependencia: Secretaría de Movilidad', 'tramites_chia_optimo') ON CONFLICT DO NOTHING;


        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '110'),
          '113',
          'Unión Temporal Circulemos Chía',
          'Subdependencia: Unión Temporal Circulemos Chía',
          'tramites_chia_optimo'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      


        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '110'),
          '112',
          'Dirección de Servicios de Movilidad y Getión del Transporte',
          'Subdependencia: Dirección de Servicios de Movilidad y Getión del Transporte',
          'tramites_chia_optimo'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      

INSERT INTO ingestion.dependencias (codigo, nombre, descripcion, fuente_original) VALUES ('010', 'Secretaría de Planeación', 'Dependencia: Secretaría de Planeación', 'tramites_chia_optimo') ON CONFLICT DO NOTHING;


        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '010'),
          '013',
          'Dirección de Ordenamiento Territorial y Plusvalía',
          'Subdependencia: Dirección de Ordenamiento Territorial y Plusvalía',
          'tramites_chia_optimo'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      


        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '010'),
          '011',
          'Dirección de Sistemas de Información y Estadística',
          'Subdependencia: Dirección de Sistemas de Información y Estadística',
          'tramites_chia_optimo'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      


        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '010'),
          '014',
          'Dirección de Urbanismo',
          'Subdependencia: Dirección de Urbanismo',
          'tramites_chia_optimo'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      


        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '080'),
          '081',
          'Dirección de Salud Pública',
          'Subdependencia: Dirección de Salud Pública',
          'tramites_chia_optimo'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      


        INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
        VALUES (
          (SELECT id FROM ingestion.dependencias WHERE codigo = '080'),
          '082',
          'Dirección de Vigilancia y Control',
          'Subdependencia: Dirección de Vigilancia y Control',
          'tramites_chia_optimo'
        ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
      


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '100'),
            '101',
            'Forestal',
            'Subdependencia: Forestal (SMA)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '100'),
            '102',
            'Control y conservación ambiental',
            'Subdependencia: Control y conservación ambiental (SMA)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '100'),
            '103',
            'Sistema hídrico',
            'Subdependencia: Sistema hídrico (SMA)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '100'),
            '104',
            'Informes Técnicos',
            'Subdependencia: Informes Técnicos (SMA)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '100'),
            '105',
            'PGIRS',
            'Subdependencia: PGIRS (SMA)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '100'),
            '106',
            'Biodiversidad',
            'Subdependencia: Biodiversidad (SMA)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '100'),
            '107',
            'Educación ambiental',
            'Subdependencia: Educación ambiental (SMA)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '100'),
            '108',
            'Visita Técnica vigilancia y control fuentes contaminantes',
            'Subdependencia: Visita Técnica vigilancia y control fuentes contaminantes (SMA)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '110'),
            '110',
            'Directo',
            'Subdependencia: Directo (SM)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '110'),
            '111',
            'Dirección de Servicios de Movilidad y Gestión del Transporte',
            'Subdependencia: Dirección de Servicios de Movilidad y Gestión del Transporte (DSMGT)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '120'),
            '120',
            'Directo',
            'Subdependencia: Directo (SPCAC)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        

INSERT INTO ingestion.dependencias (codigo, nombre, descripcion, fuente_original) VALUES ('200', 'Descentralizados', 'Dependencia: Descentralizados (SPCAC)', 'OPA-chia-optimo') ON CONFLICT DO NOTHING;


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '200'),
            '201',
            'IDUVI',
            'Subdependencia: IDUVI (SPCAC)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '200'),
            '202',
            'IMRD',
            'Subdependencia: IMRD (SPCAC)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '200'),
            '203',
            'EMSERCHIA',
            'Subdependencia: EMSERCHIA (SPCAC)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '200'),
            '204',
            'PERSONERIA',
            'Subdependencia: PERSONERIA (SPCAC)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '200'),
            '212',
            'AGUSTIN CODAZZI',
            'Subdependencia: AGUSTIN CODAZZI (SPCAC)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '200'),
            '214',
            'CUERPO DE BOMBEROS  CHIA',
            'Subdependencia: CUERPO DE BOMBEROS  CHIA (SPCAC)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '200'),
            '215',
            'DEFENSA CIVIL COLOMBIANA',
            'Subdependencia: DEFENSA CIVIL COLOMBIANA (SPCAC)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '000'),
            '000',
            'Directo',
            'Subdependencia: Directo (DA)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '000'),
            '002',
            'Oficina de Contratación',
            'Subdependencia: Oficina de Contratación (OC)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '000'),
            '003',
            'Oficina Defensa Judicial',
            'Subdependencia: Oficina Defensa Judicial (ODJ)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '000'),
            '004',
            'Oficina de Control Interno',
            'Subdependencia: Oficina de Control Interno (OCI)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '000'),
            '006',
            'Oficina Asesora de comunicación Prensa y Protocolo',
            'Subdependencia: Oficina Asesora de comunicación Prensa y Protocolo (OACPP)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '010'),
            '010',
            'Directo',
            'Subdependencia: Directo (SP)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '010'),
            '012',
            'Dirección de Planificación del Desarrollo',
            'Subdependencia: Dirección de Planificación del Desarrollo (DPD)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '010'),
            '015',
            'Dirección de Servicios Públicos',
            'Subdependencia: Dirección de Servicios Públicos (DSP)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        

INSERT INTO ingestion.dependencias (codigo, nombre, descripcion, fuente_original) VALUES ('020', 'Secretaria General', 'Dependencia: Secretaria General (SG)', 'OPA-chia-optimo') ON CONFLICT DO NOTHING;


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '020'),
            '020',
            'Directo',
            'Subdependencia: Directo (SG)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '020'),
            '021',
            'Dirección de Función Publica',
            'Subdependencia: Dirección de Función Publica (DFP)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '020'),
            '022',
            'Dirección de Servicios Administrativos',
            'Subdependencia: Dirección de Servicios Administrativos (DSA)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '020'),
            '023',
            'Dirección Centro de Atención al Ciudadano',
            'Subdependencia: Dirección Centro de Atención al Ciudadano (DCAC)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '020'),
            '024',
            'Dirección de Control Interno Disciplinario',
            'Subdependencia: Dirección de Control Interno Disciplinario (DCID)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '030'),
            '330',
            'Comisaria Primera de Familia',
            'Subdependencia: Comisaria Primera de Familia (CPF)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '030'),
            '331',
            'Comisaria Segunda de Familia',
            'Subdependencia: Comisaria Segunda de Familia (CSF)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '030'),
            '332',
            'Comisaria Tercera de Familia',
            'Subdependencia: Comisaria Tercera de Familia (CTF)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '030'),
            '333',
            'Comisaria Cuarta de Familia',
            'Subdependencia: Comisaria Cuarta de Familia (CCF)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '030'),
            '334',
            'Inspección Primera de Policía',
            'Subdependencia: Inspección Primera de Policía (IPP)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '030'),
            '335',
            'Inspección Segunda de Policía',
            'Subdependencia: Inspección Segunda de Policía (PSP)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '030'),
            '336',
            'Inspección Tercera de Policía',
            'Subdependencia: Inspección Tercera de Policía (ITP)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '030'),
            '337',
            'Inspección Cuarta de Policía',
            'Subdependencia: Inspección Cuarta de Policía (ICP)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '030'),
            '338',
            'Inspección Quinta de Policía',
            'Subdependencia: Inspección Quinta de Policía (IQP)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '030'),
            '339',
            'Inspección Sexta de Policía',
            'Subdependencia: Inspección Sexta de Policía (ISP)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '040'),
            '040',
            'Directo',
            'Subdependencia: Directo (SH)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '040'),
            '043',
            'Dirección de Tesorería',
            'Subdependencia: Dirección de Tesorería (DT)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        

INSERT INTO ingestion.dependencias (codigo, nombre, descripcion, fuente_original) VALUES ('050', 'Secretaria de Obras Publicas', 'Dependencia: Secretaria de Obras Publicas (SOP)', 'OPA-chia-optimo') ON CONFLICT DO NOTHING;


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '050'),
            '050',
            'Directo',
            'Subdependencia: Directo (SOP)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '050'),
            '051',
            'Dirección de Infraestructura',
            'Subdependencia: Dirección de Infraestructura (DI)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '050'),
            '052',
            'Dirección de Programación, Estudios y Diseños',
            'Subdependencia: Dirección de Programación, Estudios y Diseños (DPED)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '050'),
            '053',
            'Dirección de Valorización',
            'Subdependencia: Dirección de Valorización (DV)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '060'),
            '060',
            'Directo',
            'Subdependencia: Directo (SDS)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '070'),
            '070',
            'Directo',
            'Subdependencia: Directo (SE)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '070'),
            '072',
            'Dirección de Gestión y Fomento a la Educación',
            'Subdependencia: Dirección de Gestión y Fomento a la Educación (DGFE)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '080'),
            '080',
            'Directo',
            'Subdependencia: Directo (SS)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        


          INSERT INTO ingestion.subdependencias (dependencia_id, codigo, nombre, descripcion, fuente_original)
          VALUES (
            (SELECT id FROM ingestion.dependencias WHERE codigo = '090'),
            '090',
            'Directo',
            'Subdependencia: Directo (SDE)',
            'OPA-chia-optimo'
          ) ON CONFLICT (dependencia_id, codigo) DO NOTHING;
        