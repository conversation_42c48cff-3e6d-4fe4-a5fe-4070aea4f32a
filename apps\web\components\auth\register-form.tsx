'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useAuth, RegistrationSchema, type RegistrationFormData } from '@chia/auth';
import { ShimmerButton } from '@/components/ui/shimmer-button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Eye, EyeOff, User, Mail, Phone, MapPin, Calendar, FileText } from 'lucide-react';
import Link from 'next/link';

/**
 * Colombian departments and cities data
 */
const DEPARTMENTS_CITIES = {
  'Cundinamarca': ['Ch<PERSON>', 'Bogotá', 'Soacha', 'Zipaquir<PERSON>', 'Facatativ<PERSON>', 'Cajicá'],
  'Antioquia': ['Medellín', 'Bello', 'Itagüí', 'Envigado', 'Rionegro'],
  'Valle del Cauca': ['Cali', 'Palmira', 'Buenaventura', 'Tuluá', 'Cartago'],
  'Atlántico': ['Barranquilla', 'Soledad', 'Malambo', 'Puerto Colombia'],
  'Santander': ['Bucaramanga', 'Floridablanca', 'Girón', 'Piedecuesta']
};

/**
 * Document types for Colombian citizens
 */
const DOCUMENT_TYPES = [
  { value: 'CC', label: 'Cédula de Ciudadanía' },
  { value: 'CE', label: 'Cédula de Extranjería' },
  { value: 'TI', label: 'Tarjeta de Identidad' },
  { value: 'PP', label: 'Pasaporte' }
];

interface RegisterFormProps {
  onSuccess?: () => void;
  onSwitchToLogin?: () => void;
}

/**
 * Citizen registration form component
 * Handles user registration with comprehensive validation
 */
export function RegisterForm({ onSuccess, onSwitchToLogin }: RegisterFormProps) {
  const { register: registerUser, error: authError } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    trigger,
    formState: { errors, isValid }
  } = useForm<RegistrationFormData>({
    resolver: zodResolver(RegistrationSchema),
    mode: 'onChange',
    defaultValues: {
      acceptTerms: false,
      acceptPrivacyPolicy: false
    }
  });

  const watchedDepartment = watch('department');
  const availableCities = selectedDepartment ? DEPARTMENTS_CITIES[selectedDepartment as keyof typeof DEPARTMENTS_CITIES] || [] : [];

  // Debug: Log form state
  const formValues = watch();
  console.log('Form validation state:', {
    isValid,
    errors: Object.keys(errors).length > 0 ? errors : 'No errors',
    formValues: {
      firstName: formValues.firstName,
      lastName: formValues.lastName,
      email: formValues.email,
      documentType: formValues.documentType,
      documentNumber: formValues.documentNumber,
      department: formValues.department,
      city: formValues.city,
      phone: formValues.phone,
      acceptTerms: formValues.acceptTerms,
      acceptPrivacyPolicy: formValues.acceptPrivacyPolicy,
      password: formValues.password ? '***' : '',
      confirmPassword: formValues.confirmPassword ? '***' : ''
    }
  });

  /**
   * Handle form submission
   */
  const onSubmit = async (data: RegistrationFormData) => {
    try {
      setIsLoading(true);
      console.log('Starting registration with data:', {
        email: data.email,
        firstName: data.firstName,
        lastName: data.lastName,
        documentType: data.documentType,
        documentNumber: data.documentNumber,
        city: data.city,
        department: data.department
      });

      await registerUser(data);
      console.log('Registration successful');
      onSuccess?.();
    } catch (error) {
      console.error('Registration failed - Full error object:', error);
      console.error('Registration failed - Error message:', error?.message);
      console.error('Registration failed - Error code:', error?.code);
      console.error('Registration failed - Error details:', error?.details);
      console.error('Registration failed - Error stack:', error?.stack);

      // Try to extract meaningful error information
      if (error && typeof error === 'object') {
        console.error('Registration failed - Stringified error:', JSON.stringify(error, null, 2));
      }

      // Log the error type
      console.error('Registration failed - Error type:', typeof error);
      console.error('Registration failed - Error constructor:', error?.constructor?.name);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handle department selection
   */
  const handleDepartmentChange = async (department: string) => {
    setSelectedDepartment(department);
    setValue('department', department);
    setValue('city', ''); // Reset city when department changes
    await trigger(['department', 'city']); // Trigger validation
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl font-bold text-center">
          Registro Ciudadano
        </CardTitle>
        <CardDescription className="text-center">
          Crea tu cuenta para acceder a los servicios digitales de Chía
        </CardDescription>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Error Alert */}
          {authError && (
            <Alert variant="destructive">
              <AlertDescription>
                {authError.message}
              </AlertDescription>
            </Alert>
          )}

          {/* Personal Information Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <User className="h-5 w-5 text-primary" />
              <h3 className="text-lg font-semibold">Información Personal</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">Nombres *</Label>
                <Input
                  id="firstName"
                  placeholder="Ingresa tus nombres"
                  {...register('firstName')}
                  error={errors.firstName?.message}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="lastName">Apellidos *</Label>
                <Input
                  id="lastName"
                  placeholder="Ingresa tus apellidos"
                  {...register('lastName')}
                  error={errors.lastName?.message}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="documentType">Tipo de Documento *</Label>
                <Select onValueChange={async (value) => {
                  setValue('documentType', value as any);
                  await trigger('documentType');
                }}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecciona tipo de documento" />
                  </SelectTrigger>
                  <SelectContent>
                    {DOCUMENT_TYPES.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.documentType && (
                  <p className="text-sm text-destructive">{errors.documentType.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="documentNumber">Número de Documento *</Label>
                <Input
                  id="documentNumber"
                  placeholder="Número de documento"
                  {...register('documentNumber')}
                  error={errors.documentNumber?.message}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="birthDate">Fecha de Nacimiento</Label>
              <Input
                id="birthDate"
                type="date"
                {...register('birthDate')}
                error={errors.birthDate?.message}
              />
            </div>
          </div>

          {/* Contact Information Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <Mail className="h-5 w-5 text-primary" />
              <h3 className="text-lg font-semibold">Información de Contacto</h3>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Correo Electrónico *</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                {...register('email')}
                error={errors.email?.message}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone">Teléfono</Label>
              <Input
                id="phone"
                placeholder="+57 ************"
                {...register('phone')}
                error={errors.phone?.message}
              />
            </div>
          </div>

          {/* Location Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <MapPin className="h-5 w-5 text-primary" />
              <h3 className="text-lg font-semibold">Ubicación</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="department">Departamento *</Label>
                <Select onValueChange={handleDepartmentChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecciona departamento" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.keys(DEPARTMENTS_CITIES).map((dept) => (
                      <SelectItem key={dept} value={dept}>
                        {dept}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.department && (
                  <p className="text-sm text-destructive">{errors.department.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="city">Ciudad/Municipio *</Label>
                <Select
                  onValueChange={async (value) => {
                    setValue('city', value);
                    await trigger('city');
                  }}
                  disabled={!selectedDepartment}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecciona ciudad" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableCities.map((city) => (
                      <SelectItem key={city} value={city}>
                        {city}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.city && (
                  <p className="text-sm text-destructive">{errors.city.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Security Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <FileText className="h-5 w-5 text-primary" />
              <h3 className="text-lg font-semibold">Seguridad</h3>
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">Contraseña *</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="Contraseña segura"
                  {...register('password')}
                  error={errors.password?.message}
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 -translate-y-1/2"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirmar Contraseña *</Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  placeholder="Confirma tu contraseña"
                  {...register('confirmPassword')}
                  error={errors.confirmPassword?.message}
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 -translate-y-1/2"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>
          </div>

          {/* Terms and Conditions */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="acceptTerms"
                checked={watch('acceptTerms')}
                onChange={async (e) => {
                  setValue('acceptTerms', e.target.checked);
                  await trigger('acceptTerms');
                }}
              />
              <Label htmlFor="acceptTerms" className="text-sm">
                Acepto los{' '}
                <Link href="/terminos" className="text-primary hover:underline">
                  términos y condiciones
                </Link>
                {' '}*
              </Label>
            </div>
            {errors.acceptTerms && (
              <p className="text-sm text-destructive">{errors.acceptTerms.message}</p>
            )}

            <div className="flex items-center space-x-2">
              <Checkbox
                id="acceptPrivacyPolicy"
                checked={watch('acceptPrivacyPolicy')}
                onChange={async (e) => {
                  setValue('acceptPrivacyPolicy', e.target.checked);
                  await trigger('acceptPrivacyPolicy');
                }}
              />
              <Label htmlFor="acceptPrivacyPolicy" className="text-sm">
                Acepto la{' '}
                <Link href="/privacidad" className="text-primary hover:underline">
                  política de privacidad
                </Link>
                {' '}*
              </Label>
            </div>
            {errors.acceptPrivacyPolicy && (
              <p className="text-sm text-destructive">{errors.acceptPrivacyPolicy.message}</p>
            )}
          </div>

          {/* Submit Button */}
          <ShimmerButton
            type="submit"
            disabled={!isValid || isLoading}
            className="w-full"
          >
            {isLoading ? 'Creando cuenta...' : 'Crear Cuenta'}
          </ShimmerButton>

          {/* Switch to Login */}
          <div className="text-center">
            <p className="text-sm text-muted-foreground">
              ¿Ya tienes una cuenta?{' '}
              <button
                type="button"
                onClick={onSwitchToLogin}
                className="text-primary hover:underline font-medium"
              >
                Inicia sesión aquí
              </button>
            </p>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
