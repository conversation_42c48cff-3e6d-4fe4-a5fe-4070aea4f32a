'use client';

import { useState } from 'react';
import Link from 'next/link';
import { 
  DocumentTextIcon, 
  CreditCardIcon, 
  BuildingOfficeIcon,
  UserGroupIcon,
  MapIcon,
  ClockIcon,
  ArrowRightIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';

// Mock data - In real implementation, this would come from Supabase
const featuredServices = [
  {
    id: '1',
    nombre: 'Certificado de Residencia',
    descripcion_corta: 'Obtén tu certificado de residencia de forma digital',
    tipo_servicio: 'certificados',
    tiempo_estimado_minutos: 15,
    popularidad: 95,
    icon: DocumentTextIcon,
    categoria: 'Certificados',
    disponible_online: true,
  },
  {
    id: '2',
    nombre: 'Pago de Impuesto Predial',
    descripcion_corta: 'Paga tu impuesto predial en línea de manera segura',
    tipo_servicio: 'pagos',
    tiempo_estimado_minutos: 10,
    popularidad: 88,
    icon: CreditCardIcon,
    categoria: 'Pagos',
    disponible_online: true,
  },
  {
    id: '3',
    nombre: 'Licencia de Construcción',
    descripcion_corta: 'Solicita permisos para construcción y remodelación',
    tipo_servicio: 'licencias',
    tiempo_estimado_minutos: 45,
    popularidad: 72,
    icon: BuildingOfficeIcon,
    categoria: 'Licencias',
    disponible_online: false,
  },
  {
    id: '4',
    nombre: 'Registro Civil',
    descripcion_corta: 'Servicios de registro civil y documentos de identidad',
    tipo_servicio: 'registro',
    tiempo_estimado_minutos: 30,
    popularidad: 85,
    icon: UserGroupIcon,
    categoria: 'Registro',
    disponible_online: true,
  },
  {
    id: '5',
    nombre: 'Consulta Catastral',
    descripcion_corta: 'Consulta información catastral de propiedades',
    tipo_servicio: 'consultas',
    tiempo_estimado_minutos: 5,
    popularidad: 78,
    icon: MapIcon,
    categoria: 'Consultas',
    disponible_online: true,
  },
  {
    id: '6',
    nombre: 'Cita Médica',
    descripcion_corta: 'Agenda citas en centros de salud municipales',
    tipo_servicio: 'salud',
    tiempo_estimado_minutos: 20,
    popularidad: 92,
    icon: ClockIcon,
    categoria: 'Salud',
    disponible_online: true,
  },
];

const categorias = ['Todos', 'Certificados', 'Pagos', 'Licencias', 'Registro', 'Consultas', 'Salud'];

interface FeaturedServicesProps {
  onServiceSelect?: (serviceId: string) => void;
}

export default function FeaturedServices({ onServiceSelect }: FeaturedServicesProps) {
  const [selectedCategory, setSelectedCategory] = useState('Todos');
  const [showFilters, setShowFilters] = useState(false);

  const filteredServices = selectedCategory === 'Todos' 
    ? featuredServices 
    : featuredServices.filter(service => service.categoria === selectedCategory);

  const handleServiceClick = (serviceId: string) => {
    if (onServiceSelect) {
      onServiceSelect(serviceId);
    }
  };

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
            Servicios Más Solicitados
          </h2>
          <p className="mt-4 text-lg text-gray-600 max-w-2xl mx-auto">
            Accede rápidamente a los servicios municipales más utilizados por los ciudadanos de Chía
          </p>
        </div>

        {/* Category Filters */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Filtrar por categoría</h3>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="sm:hidden flex items-center gap-2 text-primary-600 hover:text-primary-700"
            >
              <FunnelIcon className="h-5 w-5" />
              Filtros
            </button>
          </div>
          
          <div className={`${showFilters ? 'block' : 'hidden'} sm:block`}>
            <div className="flex flex-wrap gap-2">
              {categorias.map((categoria) => (
                <button
                  key={categoria}
                  onClick={() => setSelectedCategory(categoria)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 ${
                    selectedCategory === categoria
                      ? 'bg-primary-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {categoria}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredServices.map((service) => {
            const IconComponent = service.icon;
            return (
              <div
                key={service.id}
                className="group bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-lg transition-all duration-200 overflow-hidden cursor-pointer"
                onClick={() => handleServiceClick(service.id)}
                role="button"
                tabIndex={0}
                aria-label={`Seleccionar servicio: ${service.nombre}`}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handleServiceClick(service.id);
                  }
                }}
              >
                <div className="p-6">
                  {/* Service Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="flex-shrink-0 w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center group-hover:bg-primary-200 transition-colors">
                        <IconComponent className="h-6 w-6 text-primary-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 group-hover:text-primary-600 transition-colors">
                          {service.nombre}
                        </h3>
                        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                          {service.categoria}
                        </span>
                      </div>
                    </div>
                    <ArrowRightIcon className="h-5 w-5 text-gray-400 group-hover:text-primary-600 group-hover:translate-x-1 transition-all" />
                  </div>

                  {/* Service Description */}
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {service.descripcion_corta}
                  </p>

                  {/* Service Metadata */}
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <div className="flex items-center gap-4">
                      {service.tiempo_estimado_minutos && (
                        <div className="flex items-center gap-1">
                          <ClockIcon className="h-4 w-4" />
                          <span>{service.tiempo_estimado_minutos} min</span>
                        </div>
                      )}
                      <div className="flex items-center gap-1">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span>{service.popularidad}% popular</span>
                      </div>
                    </div>
                    
                    {service.disponible_online && (
                      <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                        En línea
                      </span>
                    )}
                  </div>
                </div>

                {/* Popularity Bar */}
                <div className="px-6 pb-4">
                  <div className="w-full bg-gray-200 rounded-full h-1">
                    <div 
                      className="bg-primary-600 h-1 rounded-full transition-all duration-300"
                      style={{ width: `${service.popularidad}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* View All Services Link */}
        <div className="text-center mt-12">
          <Link
            href="/servicios"
            className="inline-flex items-center gap-2 bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
          >
            Ver Todos los Servicios
            <ArrowRightIcon className="h-5 w-5" />
          </Link>
        </div>
      </div>
    </section>
  );
}
