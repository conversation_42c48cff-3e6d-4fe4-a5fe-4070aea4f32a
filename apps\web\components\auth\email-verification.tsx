'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@chia/auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Mail, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';

interface EmailVerificationProps {
  onVerified?: () => void;
}

/**
 * Email verification component
 * Handles email verification process for new users
 */
export function EmailVerification({ onVerified }: EmailVerificationProps) {
  const { user, refreshSession } = useAuth();
  const [isResending, setIsResending] = useState(false);
  const [resendMessage, setResendMessage] = useState<string | null>(null);
  const [isChecking, setIsChecking] = useState(false);

  // Check verification status periodically
  useEffect(() => {
    if (!user?.emailConfirmed) {
      const interval = setInterval(async () => {
        setIsChecking(true);
        try {
          await refreshSession();
        } catch (error) {
          console.error('Error checking verification status:', error);
        } finally {
          setIsChecking(false);
        }
      }, 5000); // Check every 5 seconds

      return () => clearInterval(interval);
    }
  }, [user?.emailConfirmed, refreshSession]);

  // Call onVerified when email is confirmed
  useEffect(() => {
    if (user?.emailConfirmed && onVerified) {
      onVerified();
    }
  }, [user?.emailConfirmed, onVerified]);

  const handleResendVerification = async () => {
    if (!user?.email) return;

    setIsResending(true);
    setResendMessage(null);

    try {
      // Use Supabase client directly for resending verification
      const { createClientSupabase } = await import('@/lib/supabase');
      const supabase = createClientSupabase();
      
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: user.email,
      });

      if (error) {
        setResendMessage(`Error: ${error.message}`);
      } else {
        setResendMessage('Correo de verificación enviado. Revisa tu bandeja de entrada.');
      }
    } catch (error) {
      console.error('Error resending verification:', error);
      setResendMessage('Error al enviar el correo de verificación. Intenta de nuevo.');
    } finally {
      setIsResending(false);
    }
  };

  if (user?.emailConfirmed) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <CheckCircle className="w-6 h-6 text-green-600" />
          </div>
          <CardTitle className="text-green-800">Email Verificado</CardTitle>
          <CardDescription>
            Tu dirección de correo electrónico ha sido verificada exitosamente.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
          <Mail className="w-6 h-6 text-blue-600" />
        </div>
        <CardTitle>Verifica tu Email</CardTitle>
        <CardDescription>
          Hemos enviado un enlace de verificación a <strong>{user?.email}</strong>
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Revisa tu bandeja de entrada y haz clic en el enlace de verificación para activar tu cuenta.
          </AlertDescription>
        </Alert>

        {resendMessage && (
          <Alert variant={resendMessage.startsWith('Error') ? 'destructive' : 'default'}>
            <AlertDescription>{resendMessage}</AlertDescription>
          </Alert>
        )}

        <div className="flex flex-col space-y-2">
          <Button
            onClick={handleResendVerification}
            disabled={isResending}
            variant="outline"
            className="w-full"
          >
            {isResending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Enviando...
              </>
            ) : (
              'Reenviar correo de verificación'
            )}
          </Button>

          {isChecking && (
            <div className="flex items-center justify-center text-sm text-muted-foreground">
              <Loader2 className="mr-2 h-3 w-3 animate-spin" />
              Verificando estado...
            </div>
          )}
        </div>

        <div className="text-center text-sm text-muted-foreground">
          <p>¿No recibiste el correo?</p>
          <p>Revisa tu carpeta de spam o correo no deseado.</p>
        </div>
      </CardContent>
    </Card>
  );
}
