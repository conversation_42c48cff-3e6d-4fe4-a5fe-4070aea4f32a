'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth, useIsAuthenticated, useHasRole } from '@chia/auth';
import { Loader2 } from 'lucide-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: string;
  requiredRoles?: string[];
  fallbackPath?: string;
  loadingComponent?: React.ReactNode;
}

/**
 * Protected route component that handles authentication and authorization
 * 
 * @example
 * ```tsx
 * // Protect route for authenticated users only
 * <ProtectedRoute>
 *   <Dashboard />
 * </ProtectedRoute>
 * 
 * // Protect route for specific role
 * <ProtectedRoute requiredRole="admin">
 *   <AdminPanel />
 * </ProtectedRoute>
 * 
 * // Protect route for multiple roles
 * <ProtectedRoute requiredRoles={['admin', 'editor']}>
 *   <ContentManagement />
 * </ProtectedRoute>
 * ```
 */
export function ProtectedRoute({
  children,
  requiredRole,
  requiredRoles,
  fallbackPath = '/login',
  loadingComponent
}: ProtectedRouteProps) {
  const router = useRouter();
  const { status, user } = useAuth();
  const isAuthenticated = useIsAuthenticated();
  const hasRequiredRole = useHasRole(requiredRole || '');

  useEffect(() => {
    // Redirect to login if not authenticated
    if (status === 'unauthenticated') {
      router.push(fallbackPath);
      return;
    }

    // Check role-based access if roles are specified
    if (isAuthenticated && (requiredRole || requiredRoles)) {
      const hasAccess = requiredRole 
        ? hasRequiredRole
        : requiredRoles?.some(role => user?.role === role) || false;

      if (!hasAccess) {
        // Redirect to appropriate page based on user role
        const redirectPath = user?.role === 'ciudadano' ? '/dashboard' : '/admin';
        router.push(redirectPath);
        return;
      }
    }
  }, [status, isAuthenticated, hasRequiredRole, user, requiredRole, requiredRoles, router, fallbackPath]);

  // Show loading state
  if (status === 'loading') {
    return loadingComponent || (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">Verificando autenticación...</p>
        </div>
      </div>
    );
  }

  // Don't render if not authenticated
  if (!isAuthenticated) {
    return null;
  }

  // Check role-based access
  if (requiredRole || requiredRoles) {
    const hasAccess = requiredRole 
      ? hasRequiredRole
      : requiredRoles?.some(role => user?.role === role) || false;

    if (!hasAccess) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Acceso Denegado</h1>
            <p className="text-gray-600 mb-4">No tienes permisos para acceder a esta página.</p>
            <button
              onClick={() => router.back()}
              className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90"
            >
              Volver
            </button>
          </div>
        </div>
      );
    }
  }

  return <>{children}</>;
}
