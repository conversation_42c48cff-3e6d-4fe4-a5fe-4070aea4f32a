#!/usr/bin/env tsx
/**
 * Script de ejecución para la ingesta de datos CHIA
 * Arquitecto: Winston 🏗️
 * Fecha: 2025-01-07
 * 
 * Uso:
 * npm run ingestion:all
 * npm run ingestion:faqs
 * npm run ingestion:tramites
 * npm run ingestion:opas
 * npm run ingestion:validate
 * npm run ingestion:stats
 */

import { IngestionProcessor } from '../lib/ingestion/processor';
import { IngestionConfig, IngestionEvent } from '../lib/ingestion/types';
import path from 'path';
import { config } from 'dotenv';

// Cargar variables de entorno
config();

// =====================================================
// CONFIGURACIÓN
// =====================================================

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;
const DATA_DIRECTORY = path.join(process.cwd(), 'data');

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('❌ Error: Variables de entorno de Supabase no configuradas');
  console.error('Asegúrate de tener NEXT_PUBLIC_SUPABASE_URL y SUPABASE_SERVICE_ROLE_KEY en tu .env.local');
  process.exit(1);
}

// =====================================================
// CONFIGURACIÓN DE INGESTA
// =====================================================

const ingestionConfig: Partial<IngestionConfig> = {
  batchSize: 50,
  maxRetries: 3,
  errorThreshold: 0.05, // 5% de errores máximo
  parallelWorkers: 2,
  validateData: true,
  skipDuplicates: true,
  logLevel: 'info',
  dryRun: process.argv.includes('--dry-run')
};

// =====================================================
// MANEJADOR DE EVENTOS
// =====================================================

const eventHandler = async (event: IngestionEvent): Promise<void> => {
  const timestamp = new Date().toISOString();
  
  switch (event.type) {
    case 'started':
      console.log(`🚀 [${timestamp}] Iniciando ingesta de datos CHIA`);
      console.log(`📋 Configuración:`, {
        batchSize: event.data.config.batchSize,
        validateData: event.data.config.validateData,
        dryRun: event.data.config.dryRun
      });
      break;

    case 'file_processing':
      console.log(`📁 [${timestamp}] Procesando archivo: ${event.data.filename}`);
      break;

    case 'batch_completed':
      console.log(`✅ [${timestamp}] Lote ${event.data.batchNumber} completado: ${event.data.results.successful}/${event.data.results.processed} registros`);
      break;

    case 'progress':
      console.log(`📊 [${timestamp}] Progreso: ${event.data.percentage.toFixed(1)}% - ${event.data.message}`);
      break;

    case 'error':
      console.error(`❌ [${timestamp}] Error: ${event.data.error.message}`);
      if (event.data.error.data) {
        console.error('Datos relacionados:', JSON.stringify(event.data.error.data, null, 2));
      }
      break;

    case 'completed':
      console.log(`🎉 [${timestamp}] Ingesta completada!`);
      console.log(`📈 Estadísticas finales:`, {
        archivos: `${event.data.stats.processedFiles}/${event.data.stats.totalFiles}`,
        registros: `${event.data.stats.successfulRecords}/${event.data.stats.totalRecords}`,
        errores: event.data.stats.failedRecords,
        tasa_error: `${(event.data.stats.errorRate * 100).toFixed(2)}%`,
        throughput: `${event.data.stats.throughput.toFixed(2)} registros/seg`,
        duracion: `${(event.data.stats.duration! / 1000).toFixed(2)}s`
      });
      break;
  }
};

// =====================================================
// FUNCIONES PRINCIPALES
// =====================================================

async function runFullIngestion(): Promise<void> {
  console.log('🏗️ Winston - Arquitecto de Sistemas CHIA');
  console.log('═══════════════════════════════════════════');
  console.log('Iniciando ingesta completa de datos...\n');

  const processor = new IngestionProcessor(SUPABASE_URL, SUPABASE_SERVICE_KEY, ingestionConfig);
  processor.onEvent(eventHandler);

  try {
    const stats = await processor.processAll(DATA_DIRECTORY);
    
    if (stats.errorRate > 0.1) {
      console.warn('⚠️  Advertencia: Tasa de errores alta, revisar logs');
      process.exit(1);
    }

    console.log('\n✅ Ingesta completada exitosamente');
    
    // Mostrar estadísticas finales
    console.log('\n📊 Obteniendo estadísticas de la base de datos...');
    const dbStats = await processor.getIngestionStats();
    if (dbStats) {
      console.table(dbStats);
    }

    // Validar integridad
    console.log('\n🔍 Validando integridad de datos...');
    const integrity = await processor.validateDataIntegrity();
    if (integrity && integrity.length > 0) {
      console.warn('⚠️  Problemas de integridad encontrados:');
      console.table(integrity);
    } else {
      console.log('✅ Integridad de datos validada correctamente');
    }

  } catch (error) {
    console.error('❌ Error durante la ingesta:', error);
    process.exit(1);
  }
}

async function runSingleFileIngestion(fileType: 'faqs' | 'tramites' | 'opas'): Promise<void> {
  console.log(`🏗️ Winston - Procesando archivo de ${fileType.toUpperCase()}`);
  console.log('═══════════════════════════════════════════');

  const processor = new IngestionProcessor(SUPABASE_URL, SUPABASE_SERVICE_KEY, ingestionConfig);
  processor.onEvent(eventHandler);

  const fileMap = {
    faqs: 'faqs_chia_estructurado.json',
    tramites: 'tramites_chia_optimo.json',
    opas: 'OPA-chia-optimo.json'
  };

  const filePath = path.join(DATA_DIRECTORY, fileMap[fileType]);

  try {
    const result = await processor.processFile(filePath, fileType);
    
    if (!result.success) {
      console.error('❌ Procesamiento falló');
      console.error('Errores:', result.errors);
      process.exit(1);
    }

    console.log('✅ Archivo procesado exitosamente');
    console.log(`📊 Resultados: ${result.successful}/${result.processed} registros`);

  } catch (error) {
    console.error('❌ Error procesando archivo:', error);
    process.exit(1);
  }
}

async function showStats(): Promise<void> {
  console.log('🏗️ Winston - Estadísticas de Ingesta CHIA');
  console.log('═══════════════════════════════════════════');

  const processor = new IngestionProcessor(SUPABASE_URL, SUPABASE_SERVICE_KEY);

  try {
    const stats = await processor.getIngestionStats();
    if (stats && stats.length > 0) {
      console.log('📊 Estadísticas por tabla:');
      console.table(stats);
    } else {
      console.log('ℹ️  No hay datos de ingesta disponibles');
    }

    const integrity = await processor.validateDataIntegrity();
    if (integrity && integrity.length > 0) {
      console.log('\n⚠️  Problemas de integridad:');
      console.table(integrity);
    } else {
      console.log('\n✅ Integridad de datos OK');
    }

  } catch (error) {
    console.error('❌ Error obteniendo estadísticas:', error);
    process.exit(1);
  }
}

async function testSearch(): Promise<void> {
  console.log('🏗️ Winston - Prueba de Búsqueda CHIA');
  console.log('═══════════════════════════════════════════');

  const processor = new IngestionProcessor(SUPABASE_URL, SUPABASE_SERVICE_KEY);

  const queries = [
    'certificado',
    'impuesto predial',
    'licencia construcción',
    'registro civil'
  ];

  for (const query of queries) {
    console.log(`\n🔍 Buscando: "${query}"`);
    try {
      const results = await processor.searchContent(query, 5);
      if (results && results.length > 0) {
        results.forEach((result: any, index: number) => {
          console.log(`  ${index + 1}. [${result.tipo}] ${result.titulo}`);
          console.log(`     ${result.dependencia} - Relevancia: ${result.relevancia}`);
        });
      } else {
        console.log('  No se encontraron resultados');
      }
    } catch (error) {
      console.error(`  ❌ Error en búsqueda: ${error}`);
    }
  }
}

async function cleanup(): Promise<void> {
  console.log('🏗️ Winston - Limpieza de Datos CHIA');
  console.log('═══════════════════════════════════════════');

  const processor = new IngestionProcessor(SUPABASE_URL, SUPABASE_SERVICE_KEY);

  try {
    await processor.cleanup();
    console.log('✅ Limpieza completada');
  } catch (error) {
    console.error('❌ Error durante limpieza:', error);
    process.exit(1);
  }
}

// =====================================================
// EJECUCIÓN PRINCIPAL
// =====================================================

async function main(): Promise<void> {
  const command = process.argv[2];

  switch (command) {
    case 'all':
      await runFullIngestion();
      break;
    case 'faqs':
      await runSingleFileIngestion('faqs');
      break;
    case 'tramites':
      await runSingleFileIngestion('tramites');
      break;
    case 'opas':
      await runSingleFileIngestion('opas');
      break;
    case 'stats':
      await showStats();
      break;
    case 'search':
      await testSearch();
      break;
    case 'cleanup':
      await cleanup();
      break;
    default:
      console.log('🏗️ Winston - Sistema de Ingesta CHIA');
      console.log('═══════════════════════════════════════════');
      console.log('Comandos disponibles:');
      console.log('  all      - Procesar todos los archivos');
      console.log('  faqs     - Procesar solo FAQs');
      console.log('  tramites - Procesar solo trámites');
      console.log('  opas     - Procesar solo OPAs');
      console.log('  stats    - Mostrar estadísticas');
      console.log('  search   - Probar búsqueda');
      console.log('  cleanup  - Limpiar datos de prueba');
      console.log('\nOpciones:');
      console.log('  --dry-run - Simular sin insertar datos');
      console.log('\nEjemplos:');
      console.log('  npm run ingestion all');
      console.log('  npm run ingestion faqs --dry-run');
      console.log('  npm run ingestion stats');
      break;
  }
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Error fatal:', error);
    process.exit(1);
  });
}
