import { createClient } from '@supabase/supabase-js';
import { createClientComponentClient, createServerComponentClient } from '@supabase/auth-helpers-nextjs';

// Environment variables validation
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

// Client-side Supabase client
export const createClientSupabase = () => {
  return createClientComponentClient();
};

// Server-side Supabase client - dynamic import to avoid build issues
export const createServerSupabase = async () => {
  const { cookies } = await import('next/headers');
  const cookieStore = await cookies();
  return createServerComponentClient({ cookies: async () => cookieStore });
};

// Admin client for server-side operations
export const createAdminSupabase = () => {
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  if (!serviceRoleKey) {
    throw new Error('Missing Supabase service role key');
  }

  return createClient(supabaseUrl, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  });
};

// Default client export for backward compatibility
export const supabase = createClient(supabaseUrl, supabaseAnonKey);
