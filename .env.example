# =====================================================
# CONFIGURACIÓN DE SUPABASE PARA INGESTA CHIA
# =====================================================

# URL de tu proyecto Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url

# Clave anónima de Supabase (para el cliente)
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Clave de servicio de Supabase (para operaciones administrativas)
# ⚠️ IMPORTANTE: Esta clave tiene permisos completos, mantenerla segura
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# =====================================================
# CONFIGURACIÓN DE INGESTA (OPCIONAL)
# =====================================================

# Tamaño de lote para inserción de datos
INGESTION_BATCH_SIZE=50

# Número máximo de reintentos en caso de error
INGESTION_MAX_RETRIES=3

# Umbral de error (porcentaje máximo de errores permitidos)
INGESTION_ERROR_THRESHOLD=0.05

# Número de workers paralelos
INGESTION_PARALLEL_WORKERS=2

# Nivel de logging (debug, info, warn, error)
INGESTION_LOG_LEVEL=info

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME="CHIA - Portal Ciudadano"

# Database Configuration (for direct connections if needed)
DATABASE_URL=your_database_connection_string

# Email Configuration (if using custom email service)
SMTP_HOST=your_smtp_host
SMTP_PORT=587
SMTP_USER=your_smtp_user
SMTP_PASSWORD=your_smtp_password

# Security
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000

# Analytics (optional)
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=your_ga_id

# Environment
NODE_ENV=development

# Rate Limiting
UPSTASH_REDIS_REST_URL=your_upstash_redis_url
UPSTASH_REDIS_REST_TOKEN=your_upstash_redis_token

# Monitoring and Analytics
VERCEL_ANALYTICS_ID=your_vercel_analytics_id
SENTRY_DSN=your_sentry_dsn

# Feature Flags
ENABLE_AI_ASSISTANT=true
ENABLE_DOCUMENT_UPLOAD=true
ENABLE_NOTIFICATIONS=true

# Government Integration
GOV_API_BASE_URL=https://api.gov.co
GOV_API_KEY=your_government_api_key
CARPETA_CIUDADANA_API_URL=https://carpetaciudadana.gov.co/api

# Security Headers
CONTENT_SECURITY_POLICY_NONCE=your_csp_nonce
ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com
