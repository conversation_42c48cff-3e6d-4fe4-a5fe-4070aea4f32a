'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { RainbowButton } from '@/components/ui/rainbow-button';
import { CheckCircle, Mail, AlertCircle, RefreshCw } from 'lucide-react';
import Link from 'next/link';

/**
 * Email verification page component
 * Handles email verification after registration
 */
export default function VerifyEmailPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState<'pending' | 'success' | 'error'>('pending');
  const [errorMessage, setErrorMessage] = useState('');

  const email = searchParams.get('email');
  const token = searchParams.get('token');

  /**
   * Verify email token on component mount
   */
  useEffect(() => {
    if (token) {
      verifyEmailToken(token);
    }
  }, [token]);

  /**
   * Verify email token
   */
  const verifyEmailToken = async (verificationToken: string) => {
    try {
      setIsVerifying(true);
      
      // TODO: Implement email verification with Supabase
      // const { error } = await supabase.auth.verifyOtp({
      //   token_hash: verificationToken,
      //   type: 'email'
      // });
      
      // Simulate verification for now
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setVerificationStatus('success');
    } catch (error: any) {
      console.error('Email verification failed:', error);
      setVerificationStatus('error');
      setErrorMessage(error.message || 'Error al verificar el correo electrónico');
    } finally {
      setIsVerifying(false);
    }
  };

  /**
   * Resend verification email
   */
  const resendVerificationEmail = async () => {
    try {
      setIsVerifying(true);
      
      // TODO: Implement resend verification email
      // await authService.resendVerificationEmail(email);
      
      // Simulate resend for now
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      alert('Correo de verificación reenviado');
    } catch (error: any) {
      console.error('Failed to resend verification email:', error);
      setErrorMessage(error.message || 'Error al reenviar el correo de verificación');
    } finally {
      setIsVerifying(false);
    }
  };

  /**
   * Render verification success state
   */
  if (verificationStatus === 'success') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 flex items-center justify-center p-6">
        <Card className="w-full max-w-md mx-auto">
          <CardHeader className="text-center space-y-4">
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <CardTitle className="text-2xl font-bold text-green-900">
              ¡Correo Verificado!
            </CardTitle>
            <CardDescription>
              Tu cuenta ha sido verificada exitosamente
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-6">
            <div className="text-center space-y-4">
              <p className="text-sm text-muted-foreground">
                Tu correo electrónico ha sido confirmado. Ahora puedes acceder a todos los servicios del Portal Ciudadano Digital.
              </p>

              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h4 className="font-medium text-green-900 mb-2">¿Qué puedes hacer ahora?</h4>
                <ul className="text-sm text-green-800 space-y-1">
                  <li>• Iniciar sesión en tu cuenta</li>
                  <li>• Completar tu perfil</li>
                  <li>• Explorar los servicios disponibles</li>
                  <li>• Realizar trámites en línea</li>
                </ul>
              </div>
            </div>

            <RainbowButton
              onClick={() => router.push('/auth/login')}
              className="w-full"
            >
              Iniciar Sesión
            </RainbowButton>
          </CardContent>
        </Card>
      </div>
    );
  }

  /**
   * Render verification error state
   */
  if (verificationStatus === 'error') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50 flex items-center justify-center p-6">
        <Card className="w-full max-w-md mx-auto">
          <CardHeader className="text-center space-y-4">
            <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
              <AlertCircle className="w-8 h-8 text-red-600" />
            </div>
            <CardTitle className="text-2xl font-bold text-red-900">
              Error de Verificación
            </CardTitle>
            <CardDescription>
              No pudimos verificar tu correo electrónico
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-6">
            <Alert variant="destructive">
              <AlertDescription>
                {errorMessage || 'El enlace de verificación es inválido o ha expirado.'}
              </AlertDescription>
            </Alert>

            <div className="space-y-4">
              <RainbowButton
                onClick={resendVerificationEmail}
                disabled={isVerifying || !email}
                className="w-full"
              >
                {isVerifying ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Reenviando...
                  </>
                ) : (
                  'Reenviar Correo de Verificación'
                )}
              </RainbowButton>

              <Link
                href="/auth/login"
                className="block w-full text-center text-sm text-muted-foreground hover:text-foreground transition-colors"
              >
                Volver al inicio de sesión
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  /**
   * Render pending/loading state
   */
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 flex items-center justify-center p-6">
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
            {isVerifying ? (
              <RefreshCw className="w-8 h-8 text-blue-600 animate-spin" />
            ) : (
              <Mail className="w-8 h-8 text-blue-600" />
            )}
          </div>
          <CardTitle className="text-2xl font-bold">
            {isVerifying ? 'Verificando...' : 'Verifica tu Correo'}
          </CardTitle>
          <CardDescription>
            {isVerifying 
              ? 'Estamos verificando tu correo electrónico'
              : 'Revisa tu bandeja de entrada para completar el registro'
            }
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {!token && (
            <>
              <div className="text-center space-y-4">
                <p className="text-sm text-muted-foreground">
                  Hemos enviado un correo de verificación a{' '}
                  <strong>{email || 'tu correo electrónico'}</strong>
                </p>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900 mb-2">Instrucciones:</h4>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• Revisa tu bandeja de entrada</li>
                    <li>• Verifica la carpeta de spam</li>
                    <li>• Haz clic en el enlace de verificación</li>
                    <li>• Regresa aquí para continuar</li>
                  </ul>
                </div>
              </div>

              <div className="space-y-4">
                <RainbowButton
                  onClick={resendVerificationEmail}
                  disabled={isVerifying}
                  className="w-full"
                >
                  {isVerifying ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Reenviando...
                    </>
                  ) : (
                    'Reenviar Correo'
                  )}
                </RainbowButton>

                <Link
                  href="/auth/login"
                  className="block w-full text-center text-sm text-muted-foreground hover:text-foreground transition-colors"
                >
                  Volver al inicio de sesión
                </Link>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
