/**
 * Procesador principal de ingesta de datos CHIA
 * Arquitecto: <PERSON> 🏗️
 * Fecha: 2025-01-07
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { promises as fs } from 'fs';
import path from 'path';
import {
  IngestionConfig,
  ProcessingResult,
  ProcessingError,
  IngestionStats,
  TableStats,
  IngestionEvent,
  EventHandler,
  IngestionLog,
  DEFAULT_INGESTION_CONFIG,
  Dependencia,
  Subdependencia,
  FAQ,
  Tramite,
  OPA
} from './types';
import { DataValidator } from './validator';
import { DataTransformer } from './transformer';

// =====================================================
// PROCESADOR PRINCIPAL
// =====================================================

export class IngestionProcessor {
  private supabase: SupabaseClient;
  private validator: DataValidator;
  private transformer: DataTransformer;
  private config: IngestionConfig;
  private eventHandlers: EventHandler[] = [];
  private stats: IngestionStats;
  private isProcessing = false;

  constructor(
    supabaseUrl: string,
    supabaseKey: string,
    config: Partial<IngestionConfig> = {}
  ) {
    this.supabase = createClient(supabaseUrl, supabaseKey);
    this.validator = new DataValidator();
    this.transformer = new DataTransformer();
    this.config = { ...DEFAULT_INGESTION_CONFIG, ...config };
    this.stats = this.initializeStats();
  }

  /**
   * Registra un manejador de eventos
   */
  onEvent(handler: EventHandler): void {
    this.eventHandlers.push(handler);
  }

  /**
   * Emite un evento a todos los manejadores registrados
   */
  private async emitEvent(event: IngestionEvent): Promise<void> {
    for (const handler of this.eventHandlers) {
      try {
        await handler(event);
      } catch (error) {
        console.error('Error en manejador de eventos:', error);
      }
    }
  }

  /**
   * Inicializa las estadísticas de ingesta
   */
  private initializeStats(): IngestionStats {
    return {
      totalFiles: 0,
      processedFiles: 0,
      totalRecords: 0,
      successfulRecords: 0,
      failedRecords: 0,
      startTime: new Date(),
      errorRate: 0,
      throughput: 0
    };
  }

  /**
   * Procesa todos los archivos de ingesta
   */
  async processAll(dataDirectory: string): Promise<IngestionStats> {
    if (this.isProcessing) {
      throw new Error('Ya hay un proceso de ingesta en ejecución');
    }

    this.isProcessing = true;
    this.stats = this.initializeStats();

    try {
      await this.emitEvent({ type: 'started', data: { config: this.config } });

      // Crear log de inicio
      const logId = await this.createIngestionLog('ingesta_completa', 'todos_los_archivos');

      // Procesar archivos en orden específico
      const files = [
        { name: 'faqs_chia_estructurado.json', type: 'faqs' as const },
        { name: 'tramites_chia_optimo.json', type: 'tramites' as const },
        { name: 'OPA-chia-optimo.json', type: 'opas' as const }
      ];

      this.stats.totalFiles = files.length;

      for (const file of files) {
        const filePath = path.join(dataDirectory, file.name);
        
        try {
          await this.emitEvent({ 
            type: 'file_processing', 
            data: { filename: file.name, progress: 0 } 
          });

          const result = await this.processFile(filePath, file.type);
          
          this.stats.processedFiles++;
          this.stats.totalRecords += result.processed;
          this.stats.successfulRecords += result.successful;
          this.stats.failedRecords += result.failed;

          await this.emitEvent({
            type: 'progress',
            data: {
              percentage: (this.stats.processedFiles / this.stats.totalFiles) * 100,
              message: `Procesado ${file.name}: ${result.successful}/${result.processed} registros`
            }
          });

        } catch (error) {
          const processingError: ProcessingError = {
            type: 'unknown',
            message: `Error procesando archivo ${file.name}: ${error}`,
            timestamp: new Date().toISOString()
          };

          await this.emitEvent({ type: 'error', data: { error: processingError } });
          this.stats.failedRecords++;
        }
      }

      // Finalizar estadísticas
      this.stats.endTime = new Date();
      this.stats.duration = this.stats.endTime.getTime() - this.stats.startTime.getTime();
      this.stats.errorRate = this.stats.failedRecords / this.stats.totalRecords;
      this.stats.throughput = this.stats.totalRecords / (this.stats.duration / 1000);

      // Actualizar log
      await this.updateIngestionLog(logId, 'completado', {
        stats: this.stats,
        archivos_procesados: files.map(f => f.name)
      });

      await this.emitEvent({ type: 'completed', data: { stats: this.stats } });

      return this.stats;

    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Procesa un archivo individual
   */
  async processFile(filePath: string, type: 'faqs' | 'tramites' | 'opas'): Promise<ProcessingResult> {
    const startTime = Date.now();
    const errors: ProcessingError[] = [];

    try {
      // Leer archivo
      const fileContent = await fs.readFile(filePath, 'utf-8');
      const data = JSON.parse(fileContent);

      // Validar estructura del archivo
      let validationResult;
      switch (type) {
        case 'faqs':
          validationResult = this.validator.validateFAQFile(data);
          break;
        case 'tramites':
          validationResult = this.validator.validateTramiteFile(data);
          break;
        case 'opas':
          validationResult = this.validator.validateOPAFile(data);
          break;
      }

      if (!validationResult.valid) {
        throw new Error(`Archivo inválido: ${validationResult.errors.map(e => e.message).join(', ')}`);
      }

      // Transformar datos
      let transformedData;
      switch (type) {
        case 'faqs':
          transformedData = this.transformer.transformFAQData(data);
          break;
        case 'tramites':
          transformedData = this.transformer.transformTramiteData(data);
          break;
        case 'opas':
          transformedData = this.transformer.transformOPAData(data);
          break;
      }

      // Procesar en lotes
      const result = await this.processBatches(transformedData, type);

      return {
        success: true,
        processed: result.processed,
        successful: result.successful,
        failed: result.failed,
        errors: result.errors,
        duration: Date.now() - startTime
      };

    } catch (error) {
      const processingError: ProcessingError = {
        type: 'unknown',
        message: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      };

      return {
        success: false,
        processed: 0,
        successful: 0,
        failed: 1,
        errors: [processingError],
        duration: Date.now() - startTime
      };
    }
  }

  /**
   * Procesa datos en lotes
   */
  private async processBatches(
    data: {
      dependencias: Dependencia[];
      subdependencias: Subdependencia[];
      faqs?: FAQ[];
      tramites?: Tramite[];
      opas?: OPA[];
    },
    type: string
  ): Promise<ProcessingResult> {
    const errors: ProcessingError[] = [];
    let processed = 0;
    let successful = 0;
    let failed = 0;

    try {
      // Procesar dependencias primero
      if (data.dependencias.length > 0) {
        const depResult = await this.insertBatch('dependencias', data.dependencias);
        processed += depResult.processed;
        successful += depResult.successful;
        failed += depResult.failed;
        errors.push(...depResult.errors);
      }

      // Procesar subdependencias
      if (data.subdependencias.length > 0) {
        const subResult = await this.insertBatch('subdependencias', data.subdependencias);
        processed += subResult.processed;
        successful += subResult.successful;
        failed += subResult.failed;
        errors.push(...subResult.errors);
      }

      // Procesar datos específicos del tipo
      if (data.faqs && data.faqs.length > 0) {
        const faqResult = await this.insertBatch('faqs', data.faqs);
        processed += faqResult.processed;
        successful += faqResult.successful;
        failed += faqResult.failed;
        errors.push(...faqResult.errors);
      }

      if (data.tramites && data.tramites.length > 0) {
        const tramiteResult = await this.insertBatch('tramites', data.tramites);
        processed += tramiteResult.processed;
        successful += tramiteResult.successful;
        failed += tramiteResult.failed;
        errors.push(...tramiteResult.errors);
      }

      if (data.opas && data.opas.length > 0) {
        const opaResult = await this.insertBatch('opas', data.opas);
        processed += opaResult.processed;
        successful += opaResult.successful;
        failed += opaResult.failed;
        errors.push(...opaResult.errors);
      }

      return {
        success: failed === 0,
        processed,
        successful,
        failed,
        errors,
        duration: 0
      };

    } catch (error) {
      const processingError: ProcessingError = {
        type: 'database',
        message: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      };

      return {
        success: false,
        processed,
        successful,
        failed: processed - successful + 1,
        errors: [...errors, processingError],
        duration: 0
      };
    }
  }

  /**
   * Inserta un lote de registros en la base de datos
   */
  private async insertBatch(tableName: string, records: any[]): Promise<ProcessingResult> {
    const errors: ProcessingError[] = [];
    let successful = 0;
    let failed = 0;

    if (this.config.dryRun) {
      console.log(`[DRY RUN] Insertaría ${records.length} registros en ${tableName}`);
      return {
        success: true,
        processed: records.length,
        successful: records.length,
        failed: 0,
        errors: [],
        duration: 0
      };
    }

    // Procesar en lotes más pequeños
    const batchSize = this.config.batchSize;
    for (let i = 0; i < records.length; i += batchSize) {
      const batch = records.slice(i, i + batchSize);

      try {
        // Validar cada registro del lote
        if (this.config.validateData) {
          for (const record of batch) {
            const validation = this.validator.validate(record, tableName.slice(0, -1)); // Remove 's'
            if (!validation.valid) {
              const error: ProcessingError = {
                type: 'validation',
                message: `Validación falló: ${validation.errors.map(e => e.message).join(', ')}`,
                data: record,
                timestamp: new Date().toISOString()
              };
              errors.push(error);
              failed++;
              continue;
            }
          }
        }

        // Insertar lote
        const { error } = await this.supabase
          .from(`ingestion.${tableName}`)
          .insert(batch);

        if (error) {
          // Si hay error, intentar insertar uno por uno para identificar problemas
          for (const record of batch) {
            const { error: singleError } = await this.supabase
              .from(`ingestion.${tableName}`)
              .insert([record]);

            if (singleError) {
              const processingError: ProcessingError = {
                type: 'database',
                message: singleError.message,
                data: record,
                timestamp: new Date().toISOString()
              };
              errors.push(processingError);
              failed++;
            } else {
              successful++;
            }
          }
        } else {
          successful += batch.length;
        }

        // Emitir evento de progreso del lote
        await this.emitEvent({
          type: 'batch_completed',
          data: {
            batchNumber: Math.floor(i / batchSize) + 1,
            results: {
              success: true,
              processed: batch.length,
              successful: batch.length,
              failed: 0,
              errors: [],
              duration: 0
            }
          }
        });

      } catch (error) {
        const processingError: ProcessingError = {
          type: 'database',
          message: error instanceof Error ? error.message : String(error),
          data: batch,
          timestamp: new Date().toISOString()
        };
        errors.push(processingError);
        failed += batch.length;
      }
    }

    return {
      success: failed === 0,
      processed: records.length,
      successful,
      failed,
      errors,
      duration: 0
    };
  }

  /**
   * Crea un log de ingesta
   */
  private async createIngestionLog(proceso: string, archivo: string): Promise<string> {
    const log: Omit<IngestionLog, 'id'> = {
      proceso,
      archivo_origen: archivo,
      estado: 'iniciado',
      tiempo_inicio: new Date().toISOString(),
      metadata: {
        config: this.config,
        version: '1.0.0'
      }
    };

    const { data, error } = await this.supabase
      .from('ingestion.ingestion_logs')
      .insert([log])
      .select('id')
      .single();

    if (error) {
      console.error('Error creando log de ingesta:', error);
      return '';
    }

    return data.id;
  }

  /**
   * Actualiza un log de ingesta
   */
  private async updateIngestionLog(
    id: string,
    estado: IngestionLog['estado'],
    metadata?: Record<string, any>
  ): Promise<void> {
    if (!id) return;

    const updates: Partial<IngestionLog> = {
      estado,
      tiempo_fin: new Date().toISOString(),
      registros_procesados: this.stats.totalRecords,
      registros_exitosos: this.stats.successfulRecords,
      registros_fallidos: this.stats.failedRecords
    };

    if (metadata) {
      updates.metadata = metadata;
    }

    if (this.stats.endTime && this.stats.startTime) {
      updates.duracion = `${Math.round((this.stats.endTime.getTime() - this.stats.startTime.getTime()) / 1000)}s`;
    }

    const { error } = await this.supabase
      .from('ingestion.ingestion_logs')
      .update(updates)
      .eq('id', id);

    if (error) {
      console.error('Error actualizando log de ingesta:', error);
    }
  }

  /**
   * Obtiene estadísticas de la base de datos
   */
  async getIngestionStats(): Promise<any> {
    const { data, error } = await this.supabase
      .rpc('ingestion.obtener_estadisticas_ingesta');

    if (error) {
      console.error('Error obteniendo estadísticas:', error);
      return null;
    }

    return data;
  }

  /**
   * Valida la integridad de los datos ingresados
   */
  async validateDataIntegrity(): Promise<any> {
    const { data, error } = await this.supabase
      .rpc('ingestion.validar_integridad_datos');

    if (error) {
      console.error('Error validando integridad:', error);
      return null;
    }

    return data;
  }

  /**
   * Busca contenido en los datos ingresados
   */
  async searchContent(query: string, limit = 20, offset = 0): Promise<any> {
    const { data, error } = await this.supabase
      .rpc('ingestion.buscar_contenido', {
        p_query: query,
        p_limite: limit,
        p_offset: offset
      });

    if (error) {
      console.error('Error en búsqueda:', error);
      return null;
    }

    return data;
  }

  /**
   * Limpia datos de prueba o duplicados
   */
  async cleanup(): Promise<void> {
    if (this.config.dryRun) {
      console.log('[DRY RUN] Limpiaría datos de prueba');
      return;
    }

    // Eliminar registros de prueba
    const tables = ['opas', 'tramites', 'faqs', 'subdependencias', 'dependencias'];

    for (const table of tables) {
      const { error } = await this.supabase
        .from(`ingestion.${table}`)
        .delete()
        .eq('fuente_original', 'prueba');

      if (error) {
        console.error(`Error limpiando tabla ${table}:`, error);
      }
    }
  }

  /**
   * Obtiene el estado actual del procesador
   */
  getStatus(): {
    isProcessing: boolean;
    stats: IngestionStats;
    config: IngestionConfig;
  } {
    return {
      isProcessing: this.isProcessing,
      stats: this.stats,
      config: this.config
    };
  }
}
