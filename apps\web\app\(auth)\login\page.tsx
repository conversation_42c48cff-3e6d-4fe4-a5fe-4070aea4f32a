import { Metadata } from 'next';
import { LoginPageClient } from './login-client';

export const metadata: Metadata = {
  title: 'Iniciar Sesión | Portal Ciudadano Digital - Chía',
  description: 'Accede a tu cuenta del Portal Ciudadano Digital de Chía para gestionar tus trámites y servicios municipales.',
  keywords: ['login', 'iniciar sesión', 'portal ciudadano', 'chía', 'trámites', 'servicios municipales'],
  openGraph: {
    title: 'Iniciar Sesión | Portal Ciudadano Digital - Chía',
    description: 'Accede a tu cuenta del Portal Ciudadano Digital de Chía',
    type: 'website',
    locale: 'es_CO',
  },
  robots: {
    index: false, // Don't index auth pages
    follow: false
  }
};

/**
 * Login page component
 * Server component that renders the client-side login functionality
 */
export default function LoginPage() {
  return <LoginPageClient />;
}
