'use client';

import React, { createContext, useCallback, useEffect, useReducer } from 'react';
import { AuthService } from '../services/auth-service';
import type {
  AuthContextType,
  AuthSession,
  UserProfile,
  AuthError,
  LoginCredentials,
  RegistrationData,
  PasswordResetData,
  TwoFactorSetup,
  TwoFactorVerification
} from '../types/auth';
import { AuthStatus } from '../types/auth';

/**
 * Authentication state interface
 */
interface AuthState {
  status: AuthStatus;
  session: AuthSession | null;
  user: UserProfile | null;
  error: AuthError | null;
}

/**
 * Authentication actions
 */
type AuthAction =
  | { type: 'SET_LOADING' }
  | { type: 'SET_AUTHENTICATED'; payload: { session: AuthSession; user: UserProfile } }
  | { type: 'SET_UNAUTHENTICATED' }
  | { type: 'SET_ERROR'; payload: AuthError }
  | { type: 'CLEAR_ERROR' }
  | { type: 'UPDATE_USER'; payload: UserProfile };

/**
 * Initial authentication state
 */
const initialState: AuthState = {
  status: AuthStatus.LOADING,
  session: null,
  user: null,
  error: null
};

/**
 * Authentication reducer
 */
function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'SET_LOADING':
      return {
        ...state,
        status: AuthStatus.LOADING,
        error: null
      };

    case 'SET_AUTHENTICATED':
      return {
        ...state,
        status: AuthStatus.AUTHENTICATED,
        session: action.payload.session,
        user: action.payload.user,
        error: null
      };

    case 'SET_UNAUTHENTICATED':
      return {
        ...state,
        status: AuthStatus.UNAUTHENTICATED,
        session: null,
        user: null,
        error: null
      };

    case 'SET_ERROR':
      return {
        ...state,
        status: AuthStatus.ERROR,
        error: action.payload
      };

    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null
      };

    case 'UPDATE_USER':
      return {
        ...state,
        user: action.payload
      };

    default:
      return state;
  }
}

/**
 * Authentication context
 */
export const AuthContext = createContext<AuthContextType | null>(null);

/**
 * Authentication provider props
 */
interface AuthProviderProps {
  children: React.ReactNode;
}

/**
 * Authentication provider component
 * Manages authentication state and provides auth methods to child components
 * 
 * @example
 * ```tsx
 * function App() {
 *   return (
 *     <AuthProvider>
 *       <Router>
 *         <Routes>
 *           <Route path="/login" element={<LoginPage />} />
 *           <Route path="/dashboard" element={<ProtectedRoute><Dashboard /></ProtectedRoute>} />
 *         </Routes>
 *       </Router>
 *     </AuthProvider>
 *   );
 * }
 * ```
 */
export function AuthProvider({ children }: AuthProviderProps) {
  const [state, dispatch] = useReducer(authReducer, initialState);
  const authService = new AuthService();

  /**
   * Initialize authentication state
   */
  const initializeAuth = useCallback(async () => {
    try {
      dispatch({ type: 'SET_LOADING' });
      
      const user = await authService.getCurrentUser();
      
      if (user) {
        const session = await authService.refreshSession();
        if (session) {
          dispatch({ 
            type: 'SET_AUTHENTICATED', 
            payload: { session, user } 
          });
        } else {
          dispatch({ type: 'SET_UNAUTHENTICATED' });
        }
      } else {
        dispatch({ type: 'SET_UNAUTHENTICATED' });
      }
    } catch (error) {
      console.error('Auth initialization failed:', error);
      dispatch({ type: 'SET_UNAUTHENTICATED' });
    }
  }, []);

  /**
   * Login user
   */
  const login = useCallback(async (credentials: LoginCredentials) => {
    try {
      dispatch({ type: 'SET_LOADING' });
      dispatch({ type: 'CLEAR_ERROR' });
      
      const session = await authService.login(credentials);
      
      dispatch({ 
        type: 'SET_AUTHENTICATED', 
        payload: { session, user: session.user } 
      });
    } catch (error) {
      const authError = error as AuthError;
      dispatch({ type: 'SET_ERROR', payload: authError });
      throw authError;
    }
  }, []);

  /**
   * Register new user
   */
  const register = useCallback(async (data: RegistrationData) => {
    try {
      dispatch({ type: 'SET_LOADING' });
      dispatch({ type: 'CLEAR_ERROR' });
      
      const user = await authService.register(data);
      
      // After registration, user needs to verify email before being fully authenticated
      dispatch({ type: 'SET_UNAUTHENTICATED' });
    } catch (error) {
      const authError = error as AuthError;
      dispatch({ type: 'SET_ERROR', payload: authError });
      throw authError;
    }
  }, []);

  /**
   * Logout user
   */
  const logout = useCallback(async () => {
    try {
      await authService.logout();
      dispatch({ type: 'SET_UNAUTHENTICATED' });
    } catch (error) {
      console.error('Logout failed:', error);
      // Force logout even if server request fails
      dispatch({ type: 'SET_UNAUTHENTICATED' });
    }
  }, []);

  /**
   * Request password reset
   */
  const resetPassword = useCallback(async (email: string) => {
    try {
      dispatch({ type: 'CLEAR_ERROR' });
      await authService.requestPasswordReset(email);
    } catch (error) {
      const authError = error as AuthError;
      dispatch({ type: 'SET_ERROR', payload: authError });
      throw authError;
    }
  }, []);

  /**
   * Update password
   */
  const updatePassword = useCallback(async (data: PasswordResetData) => {
    try {
      dispatch({ type: 'CLEAR_ERROR' });
      await authService.updatePassword(data);
    } catch (error) {
      const authError = error as AuthError;
      dispatch({ type: 'SET_ERROR', payload: authError });
      throw authError;
    }
  }, []);

  /**
   * Update user profile
   */
  const updateProfile = useCallback(async (data: Partial<UserProfile>) => {
    try {
      dispatch({ type: 'CLEAR_ERROR' });
      // Implementation will be added when profile update service is ready
      throw new Error('Profile update not yet implemented');
    } catch (error) {
      const authError = error as AuthError;
      dispatch({ type: 'SET_ERROR', payload: authError });
      throw authError;
    }
  }, []);

  /**
   * Refresh authentication session
   */
  const refreshSession = useCallback(async () => {
    try {
      const session = await authService.refreshSession();
      if (session) {
        dispatch({ 
          type: 'SET_AUTHENTICATED', 
          payload: { session, user: session.user } 
        });
      } else {
        dispatch({ type: 'SET_UNAUTHENTICATED' });
      }
    } catch (error) {
      console.error('Session refresh failed:', error);
      dispatch({ type: 'SET_UNAUTHENTICATED' });
    }
  }, []);

  /**
   * Setup two-factor authentication
   */
  const setupTwoFactor = useCallback(async (): Promise<TwoFactorSetup> => {
    try {
      dispatch({ type: 'CLEAR_ERROR' });
      // Implementation will be added in 2FA service
      throw new Error('Two-factor authentication setup not yet implemented');
    } catch (error) {
      const authError = error as AuthError;
      dispatch({ type: 'SET_ERROR', payload: authError });
      throw authError;
    }
  }, []);

  /**
   * Verify two-factor authentication
   */
  const verifyTwoFactor = useCallback(async (data: TwoFactorVerification) => {
    try {
      dispatch({ type: 'CLEAR_ERROR' });
      // Implementation will be added in 2FA service
      throw new Error('Two-factor authentication verification not yet implemented');
    } catch (error) {
      const authError = error as AuthError;
      dispatch({ type: 'SET_ERROR', payload: authError });
      throw authError;
    }
  }, []);

  /**
   * Disable two-factor authentication
   */
  const disableTwoFactor = useCallback(async (password: string) => {
    try {
      dispatch({ type: 'CLEAR_ERROR' });
      // Implementation will be added in 2FA service
      throw new Error('Two-factor authentication disable not yet implemented');
    } catch (error) {
      const authError = error as AuthError;
      dispatch({ type: 'SET_ERROR', payload: authError });
      throw authError;
    }
  }, []);

  /**
   * Initialize authentication on mount
   */
  useEffect(() => {
    initializeAuth();
  }, [initializeAuth]);

  /**
   * Context value
   */
  const contextValue: AuthContextType = {
    status: state.status,
    session: state.session,
    user: state.user,
    error: state.error,
    login,
    register,
    logout,
    resetPassword,
    updatePassword,
    updateProfile,
    refreshSession,
    setupTwoFactor,
    verifyTwoFactor,
    disableTwoFactor
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}
