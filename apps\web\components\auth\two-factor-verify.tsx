'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Shield, 
  Smartphone, 
  Key, 
  AlertCircle,
  Loader2
} from 'lucide-react';

interface TwoFactorVerifyProps {
  email: string;
  onSuccess: (token: string) => void;
  onCancel?: () => void;
  onUseBackupCode?: () => void;
}

/**
 * Two-Factor Authentication verification component
 * Used during login to verify TOTP codes
 */
export function TwoFactorVerify({ email, onSuccess, onCancel, onUseBackupCode }: TwoFactorVerifyProps) {
  const [verificationCode, setVerificationCode] = useState('');
  const [backupCode, setBackupCode] = useState('');
  const [useBackup, setUseBackup] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleVerifyCode = async () => {
    const code = useBackup ? backupCode : verificationCode;
    
    if (!code || (useBackup ? code.length !== 8 : code.length !== 6)) {
      setError(useBackup ? 'Ingresa un código de respaldo válido' : 'Ingresa un código de 6 dígitos');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/auth/2fa/authenticate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email,
          code: code,
          isBackupCode: useBackup
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Código de verificación inválido');
      }

      const data = await response.json();
      onSuccess(data.token);
    } catch (error) {
      console.error('Error verifying 2FA code:', error);
      setError(error instanceof Error ? error.message : 'Error al verificar código');
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleVerifyCode();
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
          <Shield className="w-6 h-6 text-blue-600" />
        </div>
        <CardTitle>Verificación de Dos Factores</CardTitle>
        <CardDescription>
          {useBackup 
            ? 'Ingresa uno de tus códigos de respaldo'
            : 'Ingresa el código de tu aplicación de autenticación'
          }
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {!useBackup ? (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="verificationCode">Código de Autenticación</Label>
              <Input
                id="verificationCode"
                type="text"
                placeholder="123456"
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                onKeyPress={handleKeyPress}
                className="text-center text-lg font-mono tracking-widest"
                maxLength={6}
                autoComplete="one-time-code"
              />
              <div className="flex items-center text-sm text-gray-500">
                <Smartphone className="h-4 w-4 mr-1" />
                <span>Código de 6 dígitos de tu app de autenticación</span>
              </div>
            </div>

            <Button
              onClick={handleVerifyCode}
              disabled={isLoading || verificationCode.length !== 6}
              className="w-full"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Verificando...
                </>
              ) : (
                <>
                  <Key className="mr-2 h-4 w-4" />
                  Verificar Código
                </>
              )}
            </Button>

            <div className="text-center">
              <button
                type="button"
                onClick={() => setUseBackup(true)}
                className="text-sm text-blue-600 hover:text-blue-800 underline"
              >
                ¿No puedes acceder a tu dispositivo? Usar código de respaldo
              </button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="backupCode">Código de Respaldo</Label>
              <Input
                id="backupCode"
                type="text"
                placeholder="ABCD1234"
                value={backupCode}
                onChange={(e) => setBackupCode(e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '').slice(0, 8))}
                onKeyPress={handleKeyPress}
                className="text-center text-lg font-mono tracking-widest"
                maxLength={8}
              />
              <div className="flex items-center text-sm text-gray-500">
                <Key className="h-4 w-4 mr-1" />
                <span>Código de respaldo de 8 caracteres</span>
              </div>
            </div>

            <Button
              onClick={handleVerifyCode}
              disabled={isLoading || backupCode.length !== 8}
              className="w-full"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Verificando...
                </>
              ) : (
                'Usar Código de Respaldo'
              )}
            </Button>

            <div className="text-center">
              <button
                type="button"
                onClick={() => setUseBackup(false)}
                className="text-sm text-blue-600 hover:text-blue-800 underline"
              >
                Volver a código de autenticación
              </button>
            </div>
          </div>
        )}

        <div className="flex space-x-4">
          {onCancel && (
            <Button variant="outline" onClick={onCancel} className="flex-1">
              Cancelar
            </Button>
          )}
        </div>

        <Alert>
          <Shield className="h-4 w-4" />
          <AlertDescription>
            <strong>Consejo de seguridad:</strong> Nunca compartas tus códigos de autenticación o respaldo con nadie.
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
}
