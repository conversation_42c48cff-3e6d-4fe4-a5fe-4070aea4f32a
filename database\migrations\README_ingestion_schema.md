# Migración 001: Esquema de Ingesta de Datos CHIA

## 📋 Descripción General

Esta migración crea el esquema completo para la ingesta de datos del sistema CHIA, incluyendo FAQs, trámites municipales y OPAs (Organigrama de Procedimientos Administrativos) del municipio de Chía.

## 🗂️ Archivos de la Migración

- **`001_create_ingestion_schema.sql`** - Script principal de migración
- **`001_rollback_ingestion_schema.sql`** - Script de rollback
- **`001_validate_ingestion_schema.sql`** - Script de validación
- **`README_ingestion_schema.md`** - Esta documentación

## 🏗️ Estructura Creada

### Esquema: `ingestion`

#### Tablas Principales

1. **`dependencias`** - Dependencias principales del municipio
2. **`subdependencias`** - Subdependencias organizacionales
3. **`faqs`** - Preguntas frecuentes con búsqueda semántica
4. **`tramites`** - Catálogo de trámites municipales
5. **`opas`** - Organigrama de Procedimientos Administrativos
6. **`ingestion_logs`** - Logs de procesos de ingesta

#### Características Técnicas

- **UUIDs** como claves primarias para mejor escalabilidad
- **Búsqueda de texto completo** con vectores tsvector
- **Preparación para embeddings** (vector de 1536 dimensiones)
- **Auditoría completa** con timestamps y versionado
- **Soft deletes** mediante campo `activo`
- **Metadatos JSONB** para flexibilidad futura

### Funcionalidades Implementadas

#### 🔍 Búsqueda Avanzada
- Función `buscar_contenido()` para búsqueda unificada
- Índices GIN para búsqueda rápida
- Soporte para búsqueda en español

#### 📊 Estadísticas y Monitoreo
- Función `obtener_estadisticas_ingesta()`
- Función `validar_integridad_datos()`
- Logs detallados de procesos

#### 🔒 Seguridad
- Row Level Security (RLS) habilitado
- Políticas para lectura pública de datos activos
- Políticas administrativas para gestión completa

#### ⚡ Optimización
- Índices estratégicos para consultas frecuentes
- Triggers automáticos para vectores de búsqueda
- Vistas optimizadas para consultas comunes

## 🚀 Ejecución de la Migración

### Prerrequisitos
- PostgreSQL 12+ con Supabase
- Extensiones: `uuid-ossp`, `pg_trgm`
- Permisos de administrador de base de datos

### Pasos de Ejecución

1. **Ejecutar migración principal:**
```sql
\i database/migrations/001_create_ingestion_schema.sql
```

2. **Validar la migración:**
```sql
\i database/migrations/001_validate_ingestion_schema.sql
```

3. **En caso de problemas, rollback:**
```sql
\i database/migrations/001_rollback_ingestion_schema.sql
```

## 📊 Datos a Ingestar

### Archivos Fuente
- **`faqs_chia_estructurado.json`** (4,774 líneas)
- **`tramites_chia_optimo.json`** (1,298 líneas)  
- **`OPA-chia-optimo.json`** (3,422 líneas)

### Volumen Estimado
- **Dependencias:** ~50 registros
- **Subdependencias:** ~150 registros
- **FAQs:** ~500 registros
- **Trámites:** ~130 registros
- **OPAs:** ~800 registros

## 🔧 Funciones Principales

### `ingestion.buscar_contenido(query, limite, offset)`
Búsqueda unificada en FAQs, trámites y OPAs.

**Parámetros:**
- `query`: Texto a buscar
- `limite`: Número máximo de resultados (default: 20)
- `offset`: Desplazamiento para paginación (default: 0)

**Retorna:** Tabla con tipo, id, título, contenido, dependencia, subdependencia y relevancia.

### `ingestion.obtener_estadisticas_ingesta()`
Obtiene estadísticas generales de los datos ingresados.

**Retorna:** Tabla con nombre de tabla, total de registros, registros activos y última actualización.

### `ingestion.validar_integridad_datos()`
Valida la integridad referencial de los datos.

**Retorna:** Tabla con problemas encontrados y cantidades.

## 📋 Vistas Disponibles

### `ingestion.vista_estructura_organizacional`
Vista consolidada de la estructura organizacional completa.

### `ingestion.vista_faqs_completa`
Vista de FAQs con información organizacional completa.

## 🔐 Políticas de Seguridad

### Lectura Pública
- Usuarios pueden leer datos activos sin autenticación
- Filtrado automático por campo `activo = true`

### Administración
- Rol `admin` tiene acceso completo a todos los datos
- Acceso a logs de ingesta restringido a administradores

## 🚨 Consideraciones Importantes

### Rendimiento
- Los índices están optimizados para consultas frecuentes
- La búsqueda de texto completo puede ser intensiva en CPU
- Considerar particionado para volúmenes muy grandes

### Mantenimiento
- Los vectores de búsqueda se actualizan automáticamente
- Ejecutar `VACUUM ANALYZE` periódicamente
- Monitorear el crecimiento de logs de ingesta

### Escalabilidad
- Diseño preparado para millones de registros
- UUIDs permiten distribución futura
- Metadatos JSONB para evolución del esquema

## 🔄 Próximos Pasos

1. **Desarrollar procesador de ingesta** en TypeScript
2. **Implementar validación de datos** antes de inserción
3. **Configurar monitoreo** de calidad de datos
4. **Crear APIs** para acceso a los datos
5. **Implementar búsqueda semántica** con embeddings

## 📞 Soporte

Para problemas con esta migración:
1. Verificar logs en `ingestion.ingestion_logs`
2. Ejecutar script de validación
3. Consultar documentación de Supabase
4. Contactar al equipo de arquitectura

---

**Versión:** 001  
**Fecha:** 2025-01-07  
**Arquitecto:** Winston 🏗️  
**Estado:** Listo para producción
