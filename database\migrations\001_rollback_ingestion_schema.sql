-- =====================================================
-- ROLLBACK: Esquema de Ingesta de Datos CHIA
-- Versión: 001
-- Fecha: 2025-01-07
-- Descripción: Script para revertir la migración 001
--              Elimina completamente el esquema de ingesta
-- =====================================================

-- Mensaje de advertencia
DO $$
BEGIN
    RAISE WARNING 'ATENCIÓN: Este script eliminará COMPLETAMENTE el esquema de ingesta y TODOS sus datos';
    RAISE WARNING 'Asegúrese de tener un respaldo antes de continuar';
END $$;

-- =====================================================
-- ELIMINACIÓN DE VISTAS
-- =====================================================

DROP VIEW IF EXISTS ingestion.vista_faqs_completa CASCADE;
DROP VIEW IF EXISTS ingestion.vista_estructura_organizacional CASCADE;

-- =====================================================
-- ELIMINACIÓN DE FUNCIONES
-- =====================================================

DROP FUNCTION IF EXISTS ingestion.validar_integridad_datos() CASCADE;
DROP FUNCTION IF EXISTS ingestion.obtener_estadisticas_ingesta() CASCADE;
DROP FUNCTION IF EXISTS ingestion.buscar_contenido(TEXT, INTEGER, INTEGER) CASCADE;
DROP FUNCTION IF EXISTS ingestion.update_opas_search_vector() CASCADE;
DROP FUNCTION IF EXISTS ingestion.update_tramites_search_vector() CASCADE;
DROP FUNCTION IF EXISTS ingestion.update_faqs_search_vector() CASCADE;
DROP FUNCTION IF EXISTS ingestion.update_updated_at_column() CASCADE;

-- =====================================================
-- ELIMINACIÓN DE TRIGGERS
-- =====================================================

-- Triggers de vectores de búsqueda
DROP TRIGGER IF EXISTS trigger_opas_search_vector ON ingestion.opas;
DROP TRIGGER IF EXISTS trigger_tramites_search_vector ON ingestion.tramites;
DROP TRIGGER IF EXISTS trigger_faqs_search_vector ON ingestion.faqs;

-- Triggers de updated_at
DROP TRIGGER IF EXISTS trigger_opas_updated_at ON ingestion.opas;
DROP TRIGGER IF EXISTS trigger_tramites_updated_at ON ingestion.tramites;
DROP TRIGGER IF EXISTS trigger_faqs_updated_at ON ingestion.faqs;
DROP TRIGGER IF EXISTS trigger_subdependencias_updated_at ON ingestion.subdependencias;
DROP TRIGGER IF EXISTS trigger_dependencias_updated_at ON ingestion.dependencias;

-- =====================================================
-- ELIMINACIÓN DE POLÍTICAS RLS
-- =====================================================

-- Deshabilitar RLS antes de eliminar políticas
ALTER TABLE IF EXISTS ingestion.ingestion_logs DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS ingestion.opas DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS ingestion.tramites DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS ingestion.faqs DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS ingestion.subdependencias DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS ingestion.dependencias DISABLE ROW LEVEL SECURITY;

-- Eliminar políticas específicas
DROP POLICY IF EXISTS "Admin acceso logs" ON ingestion.ingestion_logs;
DROP POLICY IF EXISTS "Admin acceso completo OPAs" ON ingestion.opas;
DROP POLICY IF EXISTS "Admin acceso completo trámites" ON ingestion.tramites;
DROP POLICY IF EXISTS "Admin acceso completo FAQs" ON ingestion.faqs;
DROP POLICY IF EXISTS "Admin acceso completo subdependencias" ON ingestion.subdependencias;
DROP POLICY IF EXISTS "Admin acceso completo dependencias" ON ingestion.dependencias;

DROP POLICY IF EXISTS "Lectura pública OPAs activos" ON ingestion.opas;
DROP POLICY IF EXISTS "Lectura pública trámites activos" ON ingestion.tramites;
DROP POLICY IF EXISTS "Lectura pública FAQs activas" ON ingestion.faqs;
DROP POLICY IF EXISTS "Lectura pública subdependencias activas" ON ingestion.subdependencias;
DROP POLICY IF EXISTS "Lectura pública dependencias activas" ON ingestion.dependencias;

-- =====================================================
-- ELIMINACIÓN DE ÍNDICES
-- =====================================================

-- Los índices se eliminan automáticamente con las tablas,
-- pero los listamos para documentación
-- DROP INDEX IF EXISTS ingestion.idx_*;

-- =====================================================
-- ELIMINACIÓN DE TABLAS
-- =====================================================

-- Eliminar en orden inverso para respetar las dependencias
DROP TABLE IF EXISTS ingestion.ingestion_logs CASCADE;
DROP TABLE IF EXISTS ingestion.opas CASCADE;
DROP TABLE IF EXISTS ingestion.tramites CASCADE;
DROP TABLE IF EXISTS ingestion.faqs CASCADE;
DROP TABLE IF EXISTS ingestion.subdependencias CASCADE;
DROP TABLE IF EXISTS ingestion.dependencias CASCADE;

-- =====================================================
-- ELIMINACIÓN DEL ESQUEMA
-- =====================================================

DROP SCHEMA IF EXISTS ingestion CASCADE;

-- =====================================================
-- REVOCACIÓN DE PERMISOS
-- =====================================================

-- Los permisos se revocan automáticamente al eliminar el esquema

-- =====================================================
-- MENSAJE DE CONFIRMACIÓN
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE 'Rollback 001_rollback_ingestion_schema.sql completado';
    RAISE NOTICE 'Esquema de ingesta eliminado completamente';
    RAISE NOTICE 'Todos los datos de ingesta han sido eliminados';
    RAISE WARNING 'Recuerde restaurar desde respaldo si necesita recuperar los datos';
END $$;
