'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useAuth, PasswordResetSchema, type PasswordResetFormData } from '@chia/auth';
import { PulsatingButton } from '@/components/ui/pulsating-button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Mail, ArrowLeft, CheckCircle } from 'lucide-react';

interface ForgotPasswordFormProps {
  onBackToLogin?: () => void;
  onSuccess?: () => void;
}

/**
 * Forgot password form component
 * Handles password reset request via email
 */
export function ForgotPasswordForm({ onBackToLogin, onSuccess }: ForgotPasswordFormProps) {
  const { resetPassword, error: authError } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isValid }
  } = useForm<PasswordResetFormData>({
    resolver: zodResolver(PasswordResetSchema),
    mode: 'onChange'
  });

  const emailValue = watch('email');

  /**
   * Handle form submission
   */
  const onSubmit = async (data: PasswordResetFormData) => {
    try {
      setIsLoading(true);
      await resetPassword(data.email);
      setIsSuccess(true);
      onSuccess?.();
    } catch (error) {
      console.error('Password reset failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Success state component
   */
  if (isSuccess) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="space-y-1 text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
            <CheckCircle className="h-6 w-6 text-green-600" />
          </div>
          <CardTitle className="text-2xl font-bold">
            Correo Enviado
          </CardTitle>
          <CardDescription>
            Hemos enviado las instrucciones de recuperación a tu correo electrónico
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          <div className="text-center space-y-4">
            <p className="text-sm text-muted-foreground">
              Si tienes una cuenta con el correo <strong>{emailValue}</strong>, 
              recibirás un enlace para restablecer tu contraseña en los próximos minutos.
            </p>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">¿Qué hacer ahora?</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Revisa tu bandeja de entrada</li>
                <li>• Verifica la carpeta de spam</li>
                <li>• Haz clic en el enlace del correo</li>
                <li>• Crea una nueva contraseña segura</li>
              </ul>
            </div>

            <p className="text-xs text-muted-foreground">
              El enlace de recuperación expirará en 24 horas por seguridad.
            </p>
          </div>

          <div className="space-y-4">
            <button
              onClick={onBackToLogin}
              className="w-full flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium text-primary hover:text-primary/80 transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
              Volver al inicio de sesión
            </button>

            <button
              onClick={() => setIsSuccess(false)}
              className="w-full text-sm text-muted-foreground hover:text-foreground transition-colors"
            >
              ¿No recibiste el correo? Intentar de nuevo
            </button>
          </div>
        </CardContent>
      </Card>
    );
  }

  /**
   * Form state component
   */
  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl font-bold text-center">
          Recuperar Contraseña
        </CardTitle>
        <CardDescription className="text-center">
          Ingresa tu correo electrónico para recibir instrucciones de recuperación
        </CardDescription>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Error Alert */}
          {authError && (
            <Alert variant="destructive">
              <AlertDescription>
                {authError.message}
              </AlertDescription>
            </Alert>
          )}

          {/* Information Alert */}
          <Alert>
            <Mail className="h-4 w-4" />
            <AlertDescription>
              Te enviaremos un enlace seguro para restablecer tu contraseña.
            </AlertDescription>
          </Alert>

          {/* Email Field */}
          <div className="space-y-2">
            <Label htmlFor="email">Correo Electrónico</Label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                className="pl-10"
                {...register('email')}
                error={errors.email?.message}
              />
            </div>
            <p className="text-xs text-muted-foreground">
              Debe ser el mismo correo que usaste para registrarte
            </p>
          </div>

          {/* Submit Button */}
          <PulsatingButton
            type="submit"
            disabled={!isValid || isLoading}
            className="w-full"
          >
            {isLoading ? 'Enviando...' : 'Enviar Instrucciones'}
          </PulsatingButton>

          {/* Back to Login */}
          <button
            type="button"
            onClick={onBackToLogin}
            className="w-full flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            Volver al inicio de sesión
          </button>
        </form>

        {/* Help Section */}
        <div className="mt-6 pt-6 border-t">
          <div className="text-center space-y-2">
            <h4 className="text-sm font-medium">¿Necesitas ayuda?</h4>
            <p className="text-xs text-muted-foreground">
              Si tienes problemas para recuperar tu cuenta, contacta a nuestro equipo de soporte.
            </p>
            <div className="flex justify-center gap-4 text-xs">
              <a 
                href="mailto:<EMAIL>" 
                className="text-primary hover:underline"
              >
                <EMAIL>
              </a>
              <span className="text-muted-foreground">|</span>
              <a 
                href="tel:+576018610000" 
                className="text-primary hover:underline"
              >
                (*************
              </a>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
