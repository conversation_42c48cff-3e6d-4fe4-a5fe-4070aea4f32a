import { getServerSession, getCurrentUserProfile } from '@/lib/auth';

export default async function DashboardPage() {
  const session = await getServerSession();
  const profile = await getCurrentUserProfile();

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="border-4 border-dashed border-gray-200 rounded-lg p-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Bienvenido al Portal Ciudadano
            </h1>
            
            {profile && (
              <div className="mb-6">
                <p className="text-lg text-gray-700">
                  Hola, {profile.nombre} {profile.apellido}
                </p>
                <p className="text-sm text-gray-500">
                  {profile.email}
                </p>
              </div>
            )}
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="card">
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Trámites en Línea
                </h3>
                <p className="text-gray-600 mb-4">
                  Realiza tus trámites de manera rápida y segura desde casa.
                </p>
                <button className="btn-primary">
                  Ver Trámites
                </button>
              </div>
              
              <div className="card">
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Asistente IA
                </h3>
                <p className="text-gray-600 mb-4">
                  Obtén ayuda personalizada con nuestro asistente inteligente.
                </p>
                <button className="btn-primary">
                  Iniciar Chat
                </button>
              </div>
              
              <div className="card">
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Seguimiento
                </h3>
                <p className="text-gray-600 mb-4">
                  Consulta el estado de tus solicitudes y trámites.
                </p>
                <button className="btn-primary">
                  Ver Estado
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
