import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';

/**
 * GET /api/faqs/[id]
 * Obtiene detalle de una FAQ específica
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const supabase = await createServerSupabase();

    const { data: faq, error } = await supabase
      .from('faqs_view')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !faq) {
      return NextResponse.json(
        { error: 'FAQ no encontrada' },
        { status: 404 }
      );
    }

    // Transform data to match frontend expectations
    const transformedFaq = {
      id: faq.id,
      tema: faq.tema,
      pregunta: faq.pregunta,
      respuesta: faq.respuesta,
      palabrasClave: faq.palabras_clave || [],
      prioridad: faq.prioridad || 0,
      vistas: faq.vistas || 0,
      utilidad: faq.utilidad || 0,
      dependencia: {
        id: faq.dependencia_id,
        codigo: faq.dependencia_codigo,
        nombre: faq.dependencia_nombre,
        sigla: faq.dependencia_sigla
      },
      subdependencia: {
        id: faq.subdependencia_id,
        nombre: faq.subdependencia_nombre
      }
    };

    return NextResponse.json({
      success: true,
      data: transformedFaq
    });

  } catch (error) {
    console.error('Unexpected error in FAQ detail API:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/faqs/[id]
 * Actualiza una FAQ existente
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const updates = await request.json();

    const supabase = await createServerSupabase();

    // Verificar autenticación
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    // Obtener FAQ actual para verificar permisos
    const { data: currentFaq, error: fetchError } = await supabase
      .schema('ingestion')
      .from('faqs')
      .select('subdependencia_id, subdependencias(dependencia_id)')
      .eq('id', id)
      .single();

    if (fetchError || !currentFaq) {
      return NextResponse.json(
        { error: 'FAQ no encontrada' },
        { status: 404 }
      );
    }

    // Verificar permisos
    const { data: hasPermission } = await supabase
      .rpc('check_user_permission', {
        p_user_id: user.id,
        p_action: 'UPDATE',
        p_table_name: 'faqs',
        p_dependencia_id: (currentFaq.subdependencias as any)?.dependencia_id
      });

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Sin permisos para modificar esta FAQ' },
        { status: 403 }
      );
    }

    // Actualizar FAQ
    const { data: updatedFaq, error: updateError } = await supabase
      .schema('ingestion')
      .from('faqs')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating FAQ:', updateError);
      return NextResponse.json(
        { error: 'Error al actualizar FAQ' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: updatedFaq
    });

  } catch (error) {
    console.error('Error in FAQ PUT:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/faqs/[id]
 * Desactiva una FAQ (soft delete)
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const supabase = await createServerSupabase();

    // Verificar autenticación
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    // Obtener FAQ actual para verificar permisos
    const { data: currentFaq, error: fetchError } = await supabase
      .schema('ingestion')
      .from('faqs')
      .select('subdependencia_id, subdependencias(dependencia_id)')
      .eq('id', id)
      .single();

    if (fetchError || !currentFaq) {
      return NextResponse.json(
        { error: 'FAQ no encontrada' },
        { status: 404 }
      );
    }

    // Verificar permisos
    const { data: hasPermission } = await supabase
      .rpc('check_user_permission', {
        p_user_id: user.id,
        p_action: 'DELETE',
        p_table_name: 'faqs',
        p_dependencia_id: (currentFaq.subdependencias as any)?.dependencia_id
      });

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Sin permisos para eliminar esta FAQ' },
        { status: 403 }
      );
    }

    // Desactivar FAQ (soft delete)
    const { error: deleteError } = await supabase
      .schema('ingestion')
      .from('faqs')
      .update({
        activo: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', id);

    if (deleteError) {
      console.error('Error deactivating FAQ:', deleteError);
      return NextResponse.json(
        { error: 'Error al eliminar FAQ' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'FAQ eliminada correctamente'
    });

  } catch (error) {
    console.error('Error in FAQ DELETE:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
