'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@chia/auth';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { AdminRegisterForm } from '@/components/auth/admin-register-form';
import { MainNavigation } from '@/components/layout/MainNavigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Shield, 
  Users, 
  FileText, 
  MessageSquare, 
  Settings,
  UserPlus,
  BarChart3,
  Building2,
  ClipboardList
} from 'lucide-react';

interface DashboardStats {
  dependencias: number;
  subdependencias: number;
  tramites: number;
  opas: number;
  faqs: number;
  usuarios: number;
}

/**
 * Admin client component
 * Main administrative interface for system management
 */
export function AdminClient() {
  const { user, logout } = useAuth();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [showUserForm, setShowUserForm] = useState(false);
  const [stats, setStats] = useState<DashboardStats>({
    dependencias: 0,
    subdependencias: 0,
    tramites: 0,
    opas: 0,
    faqs: 0,
    usuarios: 0
  });

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      // Fetch basic stats from API endpoints
      const [dependenciasRes, tramitesRes, opasRes, faqsRes] = await Promise.all([
        fetch('/api/dependencias?limit=1'),
        fetch('/api/tramites?limit=1'),
        fetch('/api/opas?limit=1'),
        fetch('/api/faqs?limit=1')
      ]);

      const [dependenciasData, tramitesData, opasData, faqsData] = await Promise.all([
        dependenciasRes.json(),
        tramitesRes.json(),
        opasRes.json(),
        faqsRes.json()
      ]);

      setStats({
        dependencias: dependenciasData.pagination?.total || 0,
        subdependencias: 0, // TODO: Implement subdependencias count
        tramites: tramitesData.pagination?.total || 0,
        opas: opasData.pagination?.total || 0,
        faqs: faqsData.pagination?.total || 0,
        usuarios: 0 // TODO: Implement users count
      });
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  const handleUserCreated = (newUser: any) => {
    setShowUserForm(false);
    // Refresh stats or show success message
    fetchDashboardStats();
  };

  return (
    <ProtectedRoute requiredRoles={['admin', 'editor', 'moderador']}>
      <div className="min-h-screen bg-gray-50">
        <MainNavigation />
        
        <main className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  Panel de Administración
                </h1>
                <p className="text-gray-600">
                  Bienvenido, {user?.firstName} {user?.lastName} ({user?.role})
                </p>
              </div>
              <div className="flex items-center space-x-4">
                <Badge variant="outline" className="px-3 py-1">
                  <Shield className="h-4 w-4 mr-1" />
                  {user?.role}
                </Badge>
                <Button onClick={handleLogout} variant="outline">
                  Cerrar Sesión
                </Button>
              </div>
            </div>
          </div>

          {/* Admin Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="dashboard">
                <BarChart3 className="h-4 w-4 mr-2" />
                Dashboard
              </TabsTrigger>
              <TabsTrigger value="users">
                <Users className="h-4 w-4 mr-2" />
                Usuarios
              </TabsTrigger>
              <TabsTrigger value="content">
                <FileText className="h-4 w-4 mr-2" />
                Contenido
              </TabsTrigger>
              <TabsTrigger value="settings">
                <Settings className="h-4 w-4 mr-2" />
                Configuración
              </TabsTrigger>
            </TabsList>

            {/* Dashboard Tab */}
            <TabsContent value="dashboard" className="mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Dependencias</CardTitle>
                    <Building2 className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats.dependencias}</div>
                    <p className="text-xs text-muted-foreground">
                      Organizaciones registradas
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Trámites</CardTitle>
                    <ClipboardList className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats.tramites}</div>
                    <p className="text-xs text-muted-foreground">
                      Procedimientos disponibles
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">OPAs</CardTitle>
                    <Users className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats.opas}</div>
                    <p className="text-xs text-muted-foreground">
                      Procedimientos administrativos
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">FAQs</CardTitle>
                    <MessageSquare className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats.faqs}</div>
                    <p className="text-xs text-muted-foreground">
                      Preguntas frecuentes
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Usuarios</CardTitle>
                    <Users className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats.usuarios}</div>
                    <p className="text-xs text-muted-foreground">
                      Usuarios del sistema
                    </p>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Users Tab */}
            <TabsContent value="users" className="mt-6">
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-xl font-semibold">Gestión de Usuarios</h2>
                    <p className="text-gray-600">Administra usuarios del sistema</p>
                  </div>
                  {user?.role === 'admin' && (
                    <Button onClick={() => setShowUserForm(!showUserForm)}>
                      <UserPlus className="h-4 w-4 mr-2" />
                      {showUserForm ? 'Cancelar' : 'Crear Usuario'}
                    </Button>
                  )}
                </div>

                {showUserForm && (
                  <AdminRegisterForm
                    onSuccess={handleUserCreated}
                    onCancel={() => setShowUserForm(false)}
                  />
                )}

                {!showUserForm && (
                  <Card>
                    <CardContent className="p-6">
                      <div className="text-center py-8 text-gray-500">
                        <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>Lista de usuarios</p>
                        <p className="text-sm">Próximamente: Gestión completa de usuarios</p>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </TabsContent>

            {/* Content Tab */}
            <TabsContent value="content" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Gestión de Contenido</CardTitle>
                  <CardDescription>
                    Administra trámites, OPAs y FAQs del sistema
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8 text-gray-500">
                    <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Gestión de contenido</p>
                    <p className="text-sm">Próximamente: CRUD completo para contenido</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Settings Tab */}
            <TabsContent value="settings" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Configuración del Sistema</CardTitle>
                  <CardDescription>
                    Configuraciones generales y parámetros del sistema
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8 text-gray-500">
                    <Settings className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Configuración del sistema</p>
                    <p className="text-sm">Próximamente: Panel de configuración</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </ProtectedRoute>
  );
}
