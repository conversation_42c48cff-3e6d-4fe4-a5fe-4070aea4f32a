'use client';

import { useState, useEffect } from 'react';
import { 
  PhoneIcon, 
  EnvelopeIcon, 
  MapPinIcon, 
  ClockIcon,
  GlobeAltIcon,
  ChatBubbleLeftRightIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface ServiceHours {
  day: string;
  hours: string;
  isToday: boolean;
  isOpen: boolean;
}

interface ContactChannel {
  id: string;
  name: string;
  value: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  available: boolean;
  responseTime?: string;
}

export default function ContactInfo() {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [serviceHours, setServiceHours] = useState<ServiceHours[]>([]);

  // Update current time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  // Calculate service hours and current status
  useEffect(() => {
    const now = new Date();
    const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    const currentTimeInMinutes = currentHour * 60 + currentMinute;

    const weekDays = [
      { day: 'Domingo', start: null, end: null },
      { day: 'Lunes', start: 8 * 60, end: 17 * 60 }, // 8:00 AM - 5:00 PM
      { day: 'Martes', start: 8 * 60, end: 17 * 60 },
      { day: 'Miércoles', start: 8 * 60, end: 17 * 60 },
      { day: 'Jueves', start: 8 * 60, end: 17 * 60 },
      { day: 'Viernes', start: 8 * 60, end: 17 * 60 },
      { day: 'Sábado', start: 8 * 60, end: 12 * 60 }, // 8:00 AM - 12:00 PM
    ];

    const hours = weekDays.map((dayInfo, index) => {
      const isToday = index === currentDay;
      let isOpen = false;
      let hours = 'Cerrado';

      if (dayInfo.start !== null && dayInfo.end !== null) {
        hours = `${Math.floor(dayInfo.start / 60)}:${(dayInfo.start % 60).toString().padStart(2, '0')} - ${Math.floor(dayInfo.end / 60)}:${(dayInfo.end % 60).toString().padStart(2, '0')}`;
        
        if (isToday) {
          isOpen = currentTimeInMinutes >= dayInfo.start && currentTimeInMinutes < dayInfo.end;
        }
      }

      return {
        day: dayInfo.day,
        hours,
        isToday,
        isOpen: isToday ? isOpen : false,
      };
    });

    setServiceHours(hours);
  }, [currentTime]);

  const contactChannels: ContactChannel[] = [
    {
      id: 'phone',
      name: 'Teléfono Principal',
      value: '(*************',
      icon: PhoneIcon,
      description: 'Línea de atención ciudadana',
      available: true,
      responseTime: 'Inmediata',
    },
    {
      id: 'whatsapp',
      name: 'WhatsApp',
      value: '+57 ************',
      icon: ChatBubbleLeftRightIcon,
      description: 'Chat directo con funcionarios',
      available: true,
      responseTime: '< 30 min',
    },
    {
      id: 'email',
      name: 'Correo Electrónico',
      value: '<EMAIL>',
      icon: EnvelopeIcon,
      description: 'Consultas y solicitudes generales',
      available: true,
      responseTime: '< 24 horas',
    },
    {
      id: 'website',
      name: 'Sitio Web Oficial',
      value: 'www.chia-cundinamarca.gov.co',
      icon: GlobeAltIcon,
      description: 'Información oficial y noticias',
      available: true,
    },
  ];

  const emergencyContacts = [
    { name: 'Emergencias', number: '123', description: 'Policía, Bomberos, Cruz Roja' },
    { name: 'Línea de Emergencia Municipal', number: '(601) 884-5911', description: 'Emergencias locales 24/7' },
  ];

  const currentStatus = serviceHours.find(h => h.isToday);
  const isCurrentlyOpen = currentStatus?.isOpen || false;

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
            Contacto y Atención
          </h2>
          <p className="mt-4 text-lg text-gray-600 max-w-2xl mx-auto">
            Múltiples canales de comunicación para brindarte la mejor atención ciudadana
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Contact Channels */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">
                Canales de Atención
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {contactChannels.map((channel) => {
                  const IconComponent = channel.icon;
                  return (
                    <div
                      key={channel.id}
                      className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-sm transition-all"
                    >
                      <div className="flex items-start gap-3">
                        <div className="flex-shrink-0 w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                          <IconComponent className="h-5 w-5 text-primary-600" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-gray-900">{channel.name}</h4>
                          <p className="text-primary-600 font-medium break-all">{channel.value}</p>
                          <p className="text-sm text-gray-500 mt-1">{channel.description}</p>
                          {channel.responseTime && (
                            <div className="flex items-center gap-1 mt-2">
                              <ClockIcon className="h-4 w-4 text-gray-400" />
                              <span className="text-xs text-gray-500">
                                Respuesta: {channel.responseTime}
                              </span>
                            </div>
                          )}
                        </div>
                        <div className={`flex-shrink-0 w-2 h-2 rounded-full ${
                          channel.available ? 'bg-green-500' : 'bg-red-500'
                        }`} />
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Physical Address */}
              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <div className="flex items-start gap-3">
                  <MapPinIcon className="h-5 w-5 text-gray-600 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-gray-900">Dirección Física</h4>
                    <p className="text-gray-600">
                      Carrera 11 No. 17-25, Centro<br />
                      Chía, Cundinamarca, Colombia
                    </p>
                    <button className="text-primary-600 hover:text-primary-700 text-sm font-medium mt-2 focus:outline-none focus:underline">
                      Ver en Google Maps →
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Emergency Contacts */}
            <div className="mt-6 bg-red-50 border border-red-200 rounded-xl p-6">
              <div className="flex items-center gap-2 mb-4">
                <ExclamationTriangleIcon className="h-5 w-5 text-red-600" />
                <h3 className="text-lg font-semibold text-red-900">
                  Contactos de Emergencia
                </h3>
              </div>
              <div className="space-y-3">
                {emergencyContacts.map((contact, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-red-900">{contact.name}</p>
                      <p className="text-sm text-red-700">{contact.description}</p>
                    </div>
                    <a
                      href={`tel:${contact.number}`}
                      className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                    >
                      {contact.number}
                    </a>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Service Hours */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 sticky top-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-900">
                  Horarios de Atención
                </h3>
                <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium ${
                  isCurrentlyOpen 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  <div className={`w-2 h-2 rounded-full ${
                    isCurrentlyOpen ? 'bg-green-500' : 'bg-red-500'
                  }`} />
                  {isCurrentlyOpen ? 'Abierto' : 'Cerrado'}
                </div>
              </div>

              <div className="space-y-3">
                {serviceHours.map((schedule, index) => (
                  <div
                    key={index}
                    className={`flex items-center justify-between py-2 px-3 rounded-lg ${
                      schedule.isToday 
                        ? 'bg-primary-50 border border-primary-200' 
                        : 'hover:bg-gray-50'
                    }`}
                  >
                    <span className={`font-medium ${
                      schedule.isToday ? 'text-primary-900' : 'text-gray-700'
                    }`}>
                      {schedule.day}
                    </span>
                    <span className={`text-sm ${
                      schedule.isToday ? 'text-primary-700' : 'text-gray-600'
                    }`}>
                      {schedule.hours}
                    </span>
                  </div>
                ))}
              </div>

              {/* Current Time */}
              <div className="mt-6 pt-4 border-t border-gray-200">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Hora actual:</span>
                  <span className="font-medium text-gray-900">
                    {currentTime.toLocaleTimeString('es-CO', {
                      hour: '2-digit',
                      minute: '2-digit',
                      timeZone: 'America/Bogota'
                    })}
                  </span>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Zona horaria: Colombia (UTC-5)
                </p>
              </div>

              {/* Online Services Note */}
              <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                <div className="flex items-start gap-2">
                  <GlobeAltIcon className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-blue-900">
                      Servicios en línea 24/7
                    </p>
                    <p className="text-xs text-blue-700 mt-1">
                      Muchos trámites están disponibles las 24 horas a través de nuestro portal digital
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
