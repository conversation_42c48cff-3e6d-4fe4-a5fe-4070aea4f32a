import { Metada<PERSON> } from 'next';
import Link from 'next/link';
import PageLayout from '@/components/layout/PageLayout';
import {
  MagnifyingGlassIcon,
  DocumentMagnifyingGlassIcon,
  CreditCardIcon,
  TruckIcon,
  ArrowRightIcon,
  CheckCircleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';

export const metadata: Metadata = {
  title: 'Consultas en Línea | Portal CHIA',
  description: 'Realiza consultas en línea sobre impuestos, multas, licencias y otros trámites municipales en Chía.',
  keywords: 'consultas en línea, impuestos, multas, licencias, trámites, estado, Chía',
};

const consultationTypes = [
  {
    id: 'predial',
    name: 'Consulta Impuesto Predial',
    description: 'Consulta el estado de tu impuesto predial, avalúo y pagos realizados',
    icon: DocumentMagnifyingGlassIcon,
    placeholder: 'Número de cédula o matrícula inmobiliaria',
    popular: true
  },
  {
    id: 'multas',
    name: 'Consulta Multas de Tránsito',
    description: 'Verifica multas de tránsito pendientes por número de placa',
    icon: TruckIcon,
    placeholder: 'Número de placa del vehículo',
    popular: true
  },
  {
    id: 'licencias',
    name: 'Estado de Licencias',
    description: 'Consulta el estado de tramitación de licencias y permisos',
    icon: CreditCardIcon,
    placeholder: 'Número de radicado',
    popular: false
  },
  {
    id: 'certificados',
    name: 'Estado de Certificados',
    description: 'Verifica el estado de solicitudes de certificados',
    icon: DocumentMagnifyingGlassIcon,
    placeholder: 'Número de solicitud',
    popular: false
  }
];

const quickLinks = [
  { name: 'Avalúo Catastral', href: '#', description: 'Consulta el avalúo de tu propiedad' },
  { name: 'Paz y Salvo', href: '#', description: 'Genera certificado de paz y salvo' },
  { name: 'Historial de Pagos', href: '#', description: 'Revisa tu historial de pagos' },
  { name: 'Calendario Tributario', href: '#', description: 'Fechas importantes de pagos' }
];

export default function ConsultasPage() {
  return (
    <PageLayout className="bg-gray-50">
      {/* Breadcrumb */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-4">
              <li>
                <Link href="/" className="text-gray-400 hover:text-gray-500">
                  Inicio
                </Link>
              </li>
              <li>
                <ArrowRightIcon className="h-4 w-4 text-gray-400" />
              </li>
              <li>
                <Link href="/servicios" className="text-gray-400 hover:text-gray-500">
                  Servicios
                </Link>
              </li>
              <li>
                <ArrowRightIcon className="h-4 w-4 text-gray-400" />
              </li>
              <li>
                <span className="text-gray-900 font-medium">Consultas en Línea</span>
              </li>
            </ol>
          </nav>
        </div>
      </div>

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-indigo-600 to-indigo-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <MagnifyingGlassIcon className="h-16 w-16 mx-auto mb-4 text-indigo-200" />
            <h1 className="text-4xl font-bold mb-4">
              Consultas en Línea
            </h1>
            <p className="text-xl text-indigo-100 mb-8 max-w-3xl mx-auto">
              Consulta el estado de tus trámites, impuestos, multas y licencias 
              de forma rápida y segura, disponible las 24 horas del día.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Info Banner */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <div className="flex items-start gap-3">
            <InformationCircleIcon className="h-6 w-6 text-blue-600 flex-shrink-0 mt-0.5" />
            <div>
              <h3 className="font-semibold text-blue-900 mb-2">
                Información de Consultas
              </h3>
              <ul className="text-blue-800 text-sm space-y-1">
                <li>• Las consultas están disponibles 24/7 sin necesidad de registro</li>
                <li>• La información se actualiza en tiempo real</li>
                <li>• Puedes imprimir o descargar los resultados de tus consultas</li>
                <li>• Para consultas específicas, contacta nuestro chat de soporte</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Consultation Types */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {consultationTypes.map((type) => (
            <div key={type.id} className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 hover:shadow-lg transition-shadow">
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="p-3 bg-indigo-100 rounded-lg">
                      <type.icon className="h-6 w-6 text-indigo-600" />
                    </div>
                    {type.popular && (
                      <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">
                        Popular
                      </span>
                    )}
                  </div>
                </div>
                
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {type.name}
                </h3>
                
                <p className="text-gray-600 mb-6 text-sm leading-relaxed">
                  {type.description}
                </p>
                
                <div className="space-y-3">
                  <input
                    type="text"
                    placeholder={type.placeholder}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  />
                  <button className="w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                    Consultar
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Quick Links */}
        <div className="bg-white rounded-xl shadow-md p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            Consultas Rápidas
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {quickLinks.map((link, index) => (
              <a
                key={index}
                href={link.href}
                className="p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group"
              >
                <h3 className="font-semibold text-gray-900 mb-2 group-hover:text-indigo-700">
                  {link.name}
                </h3>
                <p className="text-gray-600 text-sm">
                  {link.description}
                </p>
              </a>
            ))}
          </div>
        </div>

        {/* Sample Results */}
        <div className="bg-white rounded-xl shadow-md p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Ejemplo de Resultado de Consulta
          </h2>
          
          <div className="bg-gray-50 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-semibold text-gray-900">Impuesto Predial - Matrícula: 123456789</h3>
              <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                Al día
              </span>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Información del Predio</h4>
                <div className="text-sm text-gray-600 space-y-1">
                  <p><strong>Dirección:</strong> Calle 123 #45-67</p>
                  <p><strong>Área:</strong> 120 m²</p>
                  <p><strong>Avalúo:</strong> $180,000,000</p>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Estado Actual</h4>
                <div className="text-sm text-gray-600 space-y-1">
                  <p><strong>Vigencia:</strong> 2024</p>
                  <p><strong>Valor a pagar:</strong> $1,800,000</p>
                  <p><strong>Estado:</strong> Pagado</p>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Último Pago</h4>
                <div className="text-sm text-gray-600 space-y-1">
                  <p><strong>Fecha:</strong> 15/03/2024</p>
                  <p><strong>Valor:</strong> $1,800,000</p>
                  <p><strong>Recibo:</strong> #789456123</p>
                </div>
              </div>
            </div>
            
            <div className="mt-6 flex gap-3">
              <button className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg text-sm font-medium transition-colors">
                Descargar Certificado
              </button>
              <button className="px-4 py-2 bg-white hover:bg-gray-50 text-indigo-600 border border-indigo-600 rounded-lg text-sm font-medium transition-colors">
                Imprimir
              </button>
            </div>
          </div>
        </div>

        {/* Help and Support */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="bg-white rounded-xl shadow-md p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              ¿No encuentras lo que buscas?
            </h3>
            <p className="text-gray-600 mb-4">
              Si no puedes encontrar la información que necesitas o tienes 
              dudas sobre los resultados de tu consulta.
            </p>
            <div className="space-y-3">
              <Link
                href="/chat"
                className="block w-full text-center bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
              >
                Consultar con IA
              </Link>
              <Link
                href="/contacto"
                className="block w-full text-center bg-white hover:bg-gray-50 text-indigo-600 border border-indigo-600 px-4 py-2 rounded-lg font-medium transition-colors"
              >
                Contactar Soporte
              </Link>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-md p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              Consejos para Consultas
            </h3>
            <ul className="space-y-3 text-gray-600 text-sm">
              <li className="flex items-start gap-2">
                <CheckCircleIcon className="h-4 w-4 text-green-500 flex-shrink-0 mt-0.5" />
                Verifica que los datos ingresados sean correctos
              </li>
              <li className="flex items-start gap-2">
                <CheckCircleIcon className="h-4 w-4 text-green-500 flex-shrink-0 mt-0.5" />
                Para placas de vehículos, incluye las letras sin espacios
              </li>
              <li className="flex items-start gap-2">
                <CheckCircleIcon className="h-4 w-4 text-green-500 flex-shrink-0 mt-0.5" />
                Los números de radicado tienen formato específico
              </li>
              <li className="flex items-start gap-2">
                <CheckCircleIcon className="h-4 w-4 text-green-500 flex-shrink-0 mt-0.5" />
                Guarda o imprime los resultados importantes
              </li>
            </ul>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
