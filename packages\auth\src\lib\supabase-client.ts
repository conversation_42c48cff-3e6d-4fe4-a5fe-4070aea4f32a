import { createClient, SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '../types/database';

/**
 * Supabase configuration interface
 */
interface SupabaseConfig {
  url: string;
  anonKey: string;
  serviceRoleKey?: string;
}

/**
 * Environment variables validation
 */
function validateEnvironment(): SupabaseConfig {
  const url = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!url) {
    throw new Error('NEXT_PUBLIC_SUPABASE_URL environment variable is required');
  }

  if (!anonKey) {
    throw new Error('NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable is required');
  }

  return {
    url,
    anonKey,
    serviceRoleKey
  };
}

/**
 * Create Supabase client for browser/client-side operations
 */
export function createSupabaseClient(): SupabaseClient<Database> {
  const config = validateEnvironment();
  
  return createClient<Database>(config.url, config.anonKey, {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true,
      flowType: 'pkce'
    },
    global: {
      headers: {
        'X-Client-Info': 'chia-portal@1.0.0'
      }
    }
  });
}

/**
 * Create Supabase admin client for server-side operations
 * Requires service role key for admin operations
 */
export function createSupabaseAdminClient(): SupabaseClient<Database> {
  const config = validateEnvironment();
  
  if (!config.serviceRoleKey) {
    throw new Error('SUPABASE_SERVICE_ROLE_KEY environment variable is required for admin operations');
  }

  return createClient<Database>(config.url, config.serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    },
    global: {
      headers: {
        'X-Client-Info': 'chia-portal-admin@1.0.0'
      }
    }
  });
}

/**
 * Singleton instance for client-side operations
 */
let supabaseClient: SupabaseClient<Database> | null = null;

/**
 * Get or create Supabase client instance
 */
export function getSupabaseClient(): SupabaseClient<Database> {
  if (!supabaseClient) {
    supabaseClient = createSupabaseClient();
  }
  return supabaseClient;
}

/**
 * Singleton instance for admin operations
 */
let supabaseAdminClient: SupabaseClient<Database> | null = null;

/**
 * Get or create Supabase admin client instance
 */
export function getSupabaseAdminClient(): SupabaseClient<Database> {
  if (!supabaseAdminClient) {
    supabaseAdminClient = createSupabaseAdminClient();
  }
  return supabaseAdminClient;
}

/**
 * Reset client instances (useful for testing)
 */
export function resetSupabaseClients(): void {
  supabaseClient = null;
  supabaseAdminClient = null;
}

/**
 * Check if we're running on the server side
 */
export function isServer(): boolean {
  return typeof window === 'undefined';
}

/**
 * Get appropriate client based on environment
 */
export function getContextualSupabaseClient(): SupabaseClient<Database> {
  return isServer() ? getSupabaseAdminClient() : getSupabaseClient();
}
