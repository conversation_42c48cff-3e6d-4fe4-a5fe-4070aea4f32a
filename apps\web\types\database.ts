// Database type definitions generated from Supabase

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      ciudadanos: {
        Row: {
          apellido: string
          auth_id: string | null
          created_at: string | null
          direccion: string | null
          documento_identidad: string
          email: string
          fecha_nacimiento: string | null
          id: string
          nombre: string
          telefono: string | null
          tipo_documento: Database["public"]["Enums"]["tipo_documento"]
          updated_at: string | null
        }
        Insert: {
          apellido: string
          auth_id?: string | null
          created_at?: string | null
          direccion?: string | null
          documento_identidad: string
          email: string
          fecha_nacimiento?: string | null
          id?: string
          nombre: string
          telefono?: string | null
          tipo_documento: Database["public"]["Enums"]["tipo_documento"]
          updated_at?: string | null
        }
        Update: {
          apellido?: string
          auth_id?: string | null
          created_at?: string | null
          direccion?: string | null
          documento_identidad?: string
          email?: string
          fecha_nacimiento?: string | null
          id?: string
          nombre?: string
          telefono?: string | null
          tipo_documento?: Database["public"]["Enums"]["tipo_documento"]
          updated_at?: string | null
        }
        Relationships: []
      }
      conversaciones_ia: {
        Row: {
          activa: boolean | null
          ciudadano_id: string | null
          created_at: string | null
          id: string
          mensajes: Json
          titulo: string | null
          updated_at: string | null
        }
        Insert: {
          activa?: boolean | null
          ciudadano_id?: string | null
          created_at?: string | null
          id?: string
          mensajes?: Json
          titulo?: string | null
          updated_at?: string | null
        }
        Update: {
          activa?: boolean | null
          ciudadano_id?: string | null
          created_at?: string | null
          id?: string
          mensajes?: Json
          titulo?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "conversaciones_ia_ciudadano_id_fkey"
            columns: ["ciudadano_id"]
            isOneToOne: false
            referencedRelation: "ciudadanos"
            referencedColumns: ["id"]
          },
        ]
      }
      servicios_ciudadanos: {
        Row: {
          activo: boolean | null
          categoria: string
          costo: number | null
          created_at: string | null
          descripcion_corta: string
          descripcion_larga: string | null
          documentos_requeridos: string[] | null
          id: string
          nombre: string
          requisitos: string[] | null
          tiempo_estimado_minutos: number | null
          tipo_servicio: Database["public"]["Enums"]["tipo_servicio"]
          updated_at: string | null
        }
        Insert: {
          activo?: boolean | null
          categoria: string
          costo?: number | null
          created_at?: string | null
          descripcion_corta: string
          descripcion_larga?: string | null
          documentos_requeridos?: string[] | null
          id?: string
          nombre: string
          requisitos?: string[] | null
          tiempo_estimado_minutos?: number | null
          tipo_servicio: Database["public"]["Enums"]["tipo_servicio"]
          updated_at?: string | null
        }
        Update: {
          activo?: boolean | null
          categoria?: string
          costo?: number | null
          created_at?: string | null
          descripcion_corta?: string
          descripcion_larga?: string | null
          documentos_requeridos?: string[] | null
          id?: string
          nombre?: string
          requisitos?: string[] | null
          tiempo_estimado_minutos?: number | null
          tipo_servicio?: Database["public"]["Enums"]["tipo_servicio"]
          updated_at?: string | null
        }
        Relationships: []
      }
      solicitudes: {
        Row: {
          ciudadano_id: string | null
          created_at: string | null
          documentos_adjuntos: string[] | null
          estado: string | null
          fecha_respuesta: string | null
          fecha_solicitud: string | null
          id: string
          numero_solicitud: string
          observaciones: string | null
          servicio_id: string | null
          updated_at: string | null
        }
        Insert: {
          ciudadano_id?: string | null
          created_at?: string | null
          documentos_adjuntos?: string[] | null
          estado?: string | null
          fecha_respuesta?: string | null
          fecha_solicitud?: string | null
          id?: string
          numero_solicitud: string
          observaciones?: string | null
          servicio_id?: string | null
          updated_at?: string | null
        }
        Update: {
          ciudadano_id?: string | null
          created_at?: string | null
          documentos_adjuntos?: string[] | null
          estado?: string | null
          fecha_respuesta?: string | null
          fecha_solicitud?: string | null
          id?: string
          numero_solicitud?: string
          observaciones?: string | null
          servicio_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "solicitudes_ciudadano_id_fkey"
            columns: ["ciudadano_id"]
            isOneToOne: false
            referencedRelation: "ciudadanos"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "solicitudes_servicio_id_fkey"
            columns: ["servicio_id"]
            isOneToOne: false
            referencedRelation: "servicios_ciudadanos"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      tipo_documento: "CC" | "CE" | "TI" | "PP"
      tipo_servicio: "tramite" | "consulta" | "pago" | "certificado"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

// Type helpers
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type TablesInsert<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type TablesUpdate<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']
export type Enums<T extends keyof Database['public']['Enums']> = Database['public']['Enums'][T]

// Specific type exports for convenience
export type Ciudadano = Tables<'ciudadanos'>
export type ServicioCiudadano = Tables<'servicios_ciudadanos'>
export type Solicitud = Tables<'solicitudes'>
export type ConversacionIA = Tables<'conversaciones_ia'>

export type TipoDocumento = Enums<'tipo_documento'>
export type TipoServicio = Enums<'tipo_servicio'>
