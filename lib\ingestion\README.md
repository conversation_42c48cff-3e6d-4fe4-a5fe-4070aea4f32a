# 🏗️ Procesador de Ingesta de Datos CHIA

**Arquitecto:** Winston 🏗️  
**Fecha:** 2025-01-07  
**Versión:** 1.0.0

## 📋 Descripción General

El procesador de ingesta de datos CHIA es un sistema robusto y escalable diseñado para transformar y cargar los datos municipales de Chía (FAQs, trámites y OPAs) en la base de datos Supabase.

## 🏗️ Arquitectura del Sistema

### Componentes Principales

1. **`types.ts`** - Definiciones de tipos TypeScript
2. **`validator.ts`** - Validación de datos de entrada
3. **`transformer.ts`** - Transformación de datos fuente
4. **`processor.ts`** - Procesador principal de ingesta
5. **`run-ingestion.ts`** - Script de ejecución

### Flujo de Procesamiento

```mermaid
graph TD
    A[Archivos JSON] --> B[Validación]
    B --> C[Transformación]
    C --> D[Procesamiento por Lotes]
    D --> E[Inserción en BD]
    E --> F[Logging y Estadísticas]
    F --> G[Validación de Integridad]
```

## 🚀 Instalación y Configuración

### 1. Instalar Dependencias

```bash
npm install @supabase/supabase-js uuid dotenv tsx
npm install -D @types/uuid
```

### 2. Configurar Variables de Entorno

```bash
cp .env.example .env.local
```

Editar `.env.local` con tus credenciales de Supabase:

```env
NEXT_PUBLIC_SUPABASE_URL=https://tu-proyecto.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=tu_clave_anonima
SUPABASE_SERVICE_ROLE_KEY=tu_clave_de_servicio
```

### 3. Ejecutar Migración de Base de Datos

```bash
# Conectar a Supabase y ejecutar migración
psql -h db.xxx.supabase.co -p 5432 -d postgres -U postgres \
  -f database/migrations/001_create_ingestion_schema.sql

# Validar migración
psql -h db.xxx.supabase.co -p 5432 -d postgres -U postgres \
  -f database/migrations/001_validate_ingestion_schema.sql
```

### 4. Preparar Archivos de Datos

Colocar los archivos JSON en el directorio `data/`:

```
data/
├── faqs_chia_estructurado.json
├── tramites_chia_optimo.json
└── OPA-chia-optimo.json
```

## 📊 Uso del Sistema

### Comandos Disponibles

```bash
# Procesar todos los archivos
npm run ingestion:all

# Procesar archivos individuales
npm run ingestion:faqs
npm run ingestion:tramites
npm run ingestion:opas

# Mostrar estadísticas
npm run ingestion:stats

# Probar búsqueda
npm run ingestion:search

# Limpiar datos de prueba
npm run ingestion:cleanup

# Simulación sin insertar datos
npm run ingestion:dry-run
```

### Ejemplo de Uso Programático

```typescript
import { IngestionProcessor } from './lib/ingestion/processor';

const processor = new IngestionProcessor(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    batchSize: 50,
    validateData: true,
    logLevel: 'info'
  }
);

// Manejar eventos
processor.onEvent(async (event) => {
  console.log('Evento:', event.type, event.data);
});

// Procesar todos los archivos
const stats = await processor.processAll('./data');
console.log('Estadísticas:', stats);
```

## 🔧 Configuración Avanzada

### Opciones de Configuración

```typescript
interface IngestionConfig {
  batchSize: number;          // Tamaño de lote (default: 100)
  maxRetries: number;         // Reintentos máximos (default: 3)
  errorThreshold: number;     // Umbral de error (default: 0.1)
  parallelWorkers: number;    // Workers paralelos (default: 4)
  validateData: boolean;      // Validar datos (default: true)
  skipDuplicates: boolean;    // Saltar duplicados (default: true)
  logLevel: string;           // Nivel de log (default: 'info')
  dryRun: boolean;           // Simulación (default: false)
}
```

### Manejo de Eventos

```typescript
processor.onEvent(async (event) => {
  switch (event.type) {
    case 'started':
      console.log('Ingesta iniciada');
      break;
    case 'file_processing':
      console.log(`Procesando: ${event.data.filename}`);
      break;
    case 'progress':
      console.log(`Progreso: ${event.data.percentage}%`);
      break;
    case 'error':
      console.error('Error:', event.data.error);
      break;
    case 'completed':
      console.log('Completado:', event.data.stats);
      break;
  }
});
```

## 📈 Monitoreo y Estadísticas

### Estadísticas Disponibles

- **Archivos procesados** vs total
- **Registros exitosos** vs fallidos
- **Tasa de error** y throughput
- **Tiempo de procesamiento**
- **Estadísticas por tabla**

### Validación de Integridad

El sistema incluye validaciones automáticas:

- Integridad referencial entre tablas
- Códigos únicos de dependencias
- Formato de datos requeridos
- Consistencia de relaciones

### Logs de Auditoría

Todos los procesos se registran en `ingestion.ingestion_logs`:

```sql
SELECT * FROM ingestion.ingestion_logs 
ORDER BY created_at DESC;
```

## 🔍 Búsqueda y Consultas

### Función de Búsqueda Unificada

```typescript
// Buscar contenido
const results = await processor.searchContent('certificado', 10);

// Resultado incluye FAQs, trámites y OPAs
results.forEach(result => {
  console.log(`[${result.tipo}] ${result.titulo}`);
  console.log(`Dependencia: ${result.dependencia}`);
  console.log(`Relevancia: ${result.relevancia}`);
});
```

### Consultas SQL Directas

```sql
-- Buscar FAQs por tema
SELECT * FROM ingestion.vista_faqs_completa 
WHERE tema ILIKE '%impuesto%';

-- Estadísticas por dependencia
SELECT dependencia, COUNT(*) as total_faqs
FROM ingestion.vista_faqs_completa
GROUP BY dependencia
ORDER BY total_faqs DESC;

-- Búsqueda de texto completo
SELECT * FROM ingestion.buscar_contenido('licencia construcción', 5);
```

## 🛠️ Mantenimiento

### Limpieza de Datos

```bash
# Limpiar datos de prueba
npm run ingestion:cleanup

# Limpiar logs antiguos (SQL)
DELETE FROM ingestion.ingestion_logs 
WHERE created_at < NOW() - INTERVAL '30 days';
```

### Optimización de Rendimiento

```sql
-- Reindexar para mejor rendimiento
REINDEX INDEX CONCURRENTLY ingestion.idx_faqs_search_vector;

-- Actualizar estadísticas
ANALYZE ingestion.faqs;
ANALYZE ingestion.tramites;
ANALYZE ingestion.opas;

-- Vacuum para liberar espacio
VACUUM ANALYZE ingestion.faqs;
```

### Respaldo y Recuperación

```bash
# Respaldo del esquema de ingesta
pg_dump -h db.xxx.supabase.co -U postgres -n ingestion > backup_ingestion.sql

# Restaurar desde respaldo
psql -h db.xxx.supabase.co -U postgres < backup_ingestion.sql
```

## 🚨 Solución de Problemas

### Errores Comunes

1. **Error de conexión a Supabase**
   - Verificar variables de entorno
   - Confirmar permisos de service_role

2. **Errores de validación**
   - Revisar formato de archivos JSON
   - Verificar campos requeridos

3. **Errores de inserción**
   - Verificar esquema de base de datos
   - Revisar políticas RLS

4. **Rendimiento lento**
   - Reducir batchSize
   - Aumentar parallelWorkers
   - Verificar índices de BD

### Debugging

```bash
# Ejecutar con logs detallados
INGESTION_LOG_LEVEL=debug npm run ingestion:all

# Simulación para debugging
npm run ingestion:dry-run

# Verificar integridad
npm run ingestion:stats
```

## 📚 Referencias

- [Documentación de Supabase](https://supabase.com/docs)
- [Migración de Base de Datos](../database/migrations/README_ingestion_schema.md)
- [Arquitectura del Sistema](../../ARQUITECTURA.md)
- [PRD del Proyecto](../../PRD_chia.md)

## 🤝 Contribución

Para contribuir al procesador de ingesta:

1. Seguir las convenciones de TypeScript
2. Agregar pruebas para nuevas funcionalidades
3. Documentar cambios en el README
4. Validar con datos de prueba antes de producción

---

**Desarrollado por Winston 🏗️ - Arquitecto de Sistemas CHIA**  
**Versión 1.0.0 - Enero 2025**
