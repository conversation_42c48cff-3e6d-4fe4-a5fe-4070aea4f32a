import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

/**
 * POST /api/auth/sessions/revoke-all
 * Revoke all sessions except the current one
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Usuario no autenticado' },
        { status: 401 }
      );
    }

    // Get current session info
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json(
        { error: 'No se pudo obtener información de la sesión actual' },
        { status: 400 }
      );
    }

    // In a real implementation, you would:
    // 1. Get all active sessions for the user
    // 2. Identify the current session
    // 3. Revoke all other sessions
    // 4. Update session storage/cache
    // 5. Notify other services

    // For now, we'll use Supabase's built-in session management
    // This will invalidate all other sessions for the user
    const { error: revokeError } = await supabase.auth.admin.signOut(user.id, 'others');

    if (revokeError) {
      console.error('Error revoking other sessions:', revokeError);
      return NextResponse.json(
        { error: 'Error al revocar sesiones' },
        { status: 500 }
      );
    }

    // Log the bulk revocation event
    const { error: logError } = await supabase
      .from('audit_logs')
      .insert({
        user_id: user.id,
        action: 'all_other_sessions_revoked',
        resource_type: 'session',
        resource_id: 'bulk_revocation',
        details: {
          revoked_at: new Date().toISOString(),
          current_session_id: session.access_token.substring(0, 16) + '...',
          action_type: 'security_cleanup'
        }
      });

    if (logError) {
      console.error('Error logging bulk session revocation:', logError);
    }

    return NextResponse.json({
      success: true,
      message: 'Todas las otras sesiones han sido revocadas',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error revoking all sessions:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
