import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

/**
 * GET /api/admin/security
 * Get security status and RLS policy information (admin only)
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Usuario no autenticado' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const { data: userRole } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', user.id)
      .single();

    if (!userRole || userRole.role !== 'admin') {
      return NextResponse.json(
        { error: 'Acceso denegado. Se requieren permisos de administrador.' },
        { status: 403 }
      );
    }

    // Get RLS policy information
    const { data: policies, error: policiesError } = await supabase
      .rpc('get_rls_policies_info');

    if (policiesError) {
      console.error('Error fetching RLS policies:', policiesError);
    }

    // Get table security status
    const { data: tables, error: tablesError } = await supabase
      .rpc('get_table_security_status');

    if (tablesError) {
      console.error('Error fetching table security status:', tablesError);
    }

    // Get recent security events
    const { data: securityEvents, error: eventsError } = await supabase
      .from('audit_logs')
      .select('*')
      .eq('resource_type', 'security')
      .order('created_at', { ascending: false })
      .limit(50);

    if (eventsError) {
      console.error('Error fetching security events:', eventsError);
    }

    // Get user statistics
    const { data: userStats, error: statsError } = await supabase
      .rpc('get_user_security_stats');

    if (statsError) {
      console.error('Error fetching user stats:', statsError);
    }

    return NextResponse.json({
      policies: policies || [],
      tables: tables || [],
      securityEvents: securityEvents || [],
      userStats: userStats || {},
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in security endpoint:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/security/test
 * Test RLS policies (admin only)
 */
export async function POST(request: NextRequest) {
  try {
    const { testType, tableNames } = await request.json();

    const supabase = createRouteHandlerClient({ cookies });

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Usuario no autenticado' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const { data: userRole } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', user.id)
      .single();

    if (!userRole || userRole.role !== 'admin') {
      return NextResponse.json(
        { error: 'Acceso denegado. Se requieren permisos de administrador.' },
        { status: 403 }
      );
    }

    const testResults = [];

    // Test basic RLS functionality
    if (testType === 'basic' || testType === 'all') {
      // Test ciudadanos table access
      try {
        const { data, error } = await supabase
          .from('ciudadanos')
          .select('id, nombre, apellido')
          .limit(1);

        testResults.push({
          test: 'ciudadanos_access',
          status: error ? 'failed' : 'passed',
          message: error ? error.message : 'Admin can access ciudadanos table',
          data: data?.length || 0
        });
      } catch (error) {
        testResults.push({
          test: 'ciudadanos_access',
          status: 'error',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }

      // Test user_roles table access
      try {
        const { data, error } = await supabase
          .from('user_roles')
          .select('user_id, role')
          .limit(5);

        testResults.push({
          test: 'user_roles_access',
          status: error ? 'failed' : 'passed',
          message: error ? error.message : 'Admin can access user_roles table',
          data: data?.length || 0
        });
      } catch (error) {
        testResults.push({
          test: 'user_roles_access',
          status: 'error',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }

      // Test audit_logs table access
      try {
        const { data, error } = await supabase
          .from('audit_logs')
          .select('id, action, created_at')
          .limit(5);

        testResults.push({
          test: 'audit_logs_access',
          status: error ? 'failed' : 'passed',
          message: error ? error.message : 'Admin can access audit_logs table',
          data: data?.length || 0
        });
      } catch (error) {
        testResults.push({
          test: 'audit_logs_access',
          status: 'error',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // Test 2FA security
    if (testType === '2fa' || testType === 'all') {
      try {
        const { data, error } = await supabase
          .from('user_two_factor')
          .select('user_id, enabled, created_at')
          .limit(5);

        testResults.push({
          test: '2fa_access',
          status: error ? 'failed' : 'passed',
          message: error ? error.message : 'Admin can access 2FA settings',
          data: data?.length || 0
        });
      } catch (error) {
        testResults.push({
          test: '2fa_access',
          status: 'error',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // Log security test
    await supabase
      .from('audit_logs')
      .insert({
        user_id: user.id,
        action: 'security_test_performed',
        resource_type: 'security',
        resource_id: 'rls_policies',
        details: { 
          test_type: testType,
          results_count: testResults.length,
          timestamp: new Date().toISOString()
        }
      });

    return NextResponse.json({
      testResults,
      summary: {
        total: testResults.length,
        passed: testResults.filter(r => r.status === 'passed').length,
        failed: testResults.filter(r => r.status === 'failed').length,
        errors: testResults.filter(r => r.status === 'error').length
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in security test endpoint:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
