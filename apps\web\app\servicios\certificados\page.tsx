import { Metadata } from 'next';
import Link from 'next/link';
import PageLayout from '@/components/layout/PageLayout';
import {
  DocumentTextIcon,
  ClockIcon,
  CheckCircleIcon,
  InformationCircleIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';

export const metadata: Metadata = {
  title: 'Certificados | Portal CHIA',
  description: 'Solicita certificados de residencia, nacimiento, defunción y otros documentos oficiales del municipio de Chía.',
  keywords: 'certificados, residencia, nacimiento, defunción, documentos oficiales, Chía',
};

const certificates = [
  {
    id: 'residencia',
    name: 'Certificado de Residencia',
    description: 'Documento que certifica tu lugar de residencia en el municipio de Chía',
    price: '$15,000',
    time: '2-3 días hábiles',
    requirements: [
      'Documento de identidad vigente',
      'Recibo de servicios públicos (no mayor a 3 meses)',
      'Formulario de solicitud diligenciado'
    ],
    popular: true
  },
  {
    id: 'nacimiento',
    name: 'Certificado de Nacimiento',
    description: 'Copia del registro civil de nacimiento expedido por la Registraduría',
    price: '$12,000',
    time: '1-2 días hábiles',
    requirements: [
      'Documento de identidad del solicitante',
      'Datos completos de la persona (nombres, fecha y lugar de nacimiento)',
      'Parentesco con la persona (si aplica)'
    ],
    popular: true
  },
  {
    id: 'defuncion',
    name: 'Certificado de Defunción',
    description: 'Documento oficial que certifica el fallecimiento de una persona',
    price: '$12,000',
    time: '1-2 días hábiles',
    requirements: [
      'Documento de identidad del solicitante',
      'Datos completos del fallecido',
      'Parentesco con el fallecido'
    ],
    popular: false
  },
  {
    id: 'matrimonio',
    name: 'Certificado de Matrimonio',
    description: 'Copia del acta de matrimonio civil o religioso',
    price: '$12,000',
    time: '1-2 días hábiles',
    requirements: [
      'Documento de identidad de los contrayentes',
      'Fecha y lugar del matrimonio',
      'Nombres completos de los contrayentes'
    ],
    popular: false
  },
  {
    id: 'solteria',
    name: 'Certificado de Soltería',
    description: 'Documento que certifica el estado civil de soltería',
    price: '$15,000',
    time: '3-5 días hábiles',
    requirements: [
      'Documento de identidad vigente',
      'Declaración juramentada',
      'Certificado de nacimiento'
    ],
    popular: false
  }
];

export default function CertificadosPage() {
  return (
    <PageLayout className="bg-gray-50">
      {/* Breadcrumb */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-4">
              <li>
                <Link href="/" className="text-gray-400 hover:text-gray-500">
                  Inicio
                </Link>
              </li>
              <li>
                <ArrowRightIcon className="h-4 w-4 text-gray-400" />
              </li>
              <li>
                <Link href="/servicios" className="text-gray-400 hover:text-gray-500">
                  Servicios
                </Link>
              </li>
              <li>
                <ArrowRightIcon className="h-4 w-4 text-gray-400" />
              </li>
              <li>
                <span className="text-gray-900 font-medium">Certificados</span>
              </li>
            </ol>
          </nav>
        </div>
      </div>

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <DocumentTextIcon className="h-16 w-16 mx-auto mb-4 text-primary-200" />
            <h1 className="text-4xl font-bold mb-4">
              Certificados y Documentos Oficiales
            </h1>
            <p className="text-xl text-primary-100 mb-8 max-w-3xl mx-auto">
              Solicita tus certificados de manera rápida y segura. Todos nuestros documentos 
              tienen validez legal y pueden ser verificados digitalmente.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Info Banner */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <div className="flex items-start gap-3">
            <InformationCircleIcon className="h-6 w-6 text-blue-600 flex-shrink-0 mt-0.5" />
            <div>
              <h3 className="font-semibold text-blue-900 mb-2">
                Información Importante
              </h3>
              <ul className="text-blue-800 text-sm space-y-1">
                <li>• Los certificados digitales tienen la misma validez legal que los físicos</li>
                <li>• Puedes verificar la autenticidad de cualquier documento en nuestro portal</li>
                <li>• Los tiempos de entrega pueden variar según la demanda</li>
                <li>• Todos los pagos se procesan de forma segura</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Certificates Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {certificates.map((cert) => (
            <div key={cert.id} className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 hover:shadow-lg transition-shadow">
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="p-3 bg-primary-100 rounded-lg">
                      <DocumentTextIcon className="h-6 w-6 text-primary-600" />
                    </div>
                    {cert.popular && (
                      <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">
                        Popular
                      </span>
                    )}
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-primary-600">{cert.price}</div>
                    <div className="text-sm text-gray-500">Precio</div>
                  </div>
                </div>
                
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {cert.name}
                </h3>
                
                <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                  {cert.description}
                </p>
                
                <div className="flex items-center gap-4 text-sm text-gray-500 mb-6">
                  <div className="flex items-center gap-1">
                    <ClockIcon className="h-4 w-4" />
                    {cert.time}
                  </div>
                </div>
                
                <div className="mb-6">
                  <h4 className="font-medium text-gray-900 mb-3">Requisitos:</h4>
                  <ul className="space-y-2">
                    {cert.requirements.map((req, index) => (
                      <li key={index} className="flex items-start gap-2 text-sm text-gray-600">
                        <CheckCircleIcon className="h-4 w-4 text-green-500 flex-shrink-0 mt-0.5" />
                        {req}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
              
              <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
                <button className="w-full bg-primary-600 hover:bg-primary-700 text-white px-4 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                  Solicitar Certificado
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Process Steps */}
        <div className="bg-white rounded-xl shadow-md p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            ¿Cómo solicitar tu certificado?
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-primary-600 font-bold text-lg">1</span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Selecciona</h3>
              <p className="text-gray-600 text-sm">Elige el tipo de certificado que necesitas</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-primary-600 font-bold text-lg">2</span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Completa</h3>
              <p className="text-gray-600 text-sm">Llena el formulario con tus datos</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-primary-600 font-bold text-lg">3</span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Paga</h3>
              <p className="text-gray-600 text-sm">Realiza el pago de forma segura</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-primary-600 font-bold text-lg">4</span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Recibe</h3>
              <p className="text-gray-600 text-sm">Descarga tu certificado digital</p>
            </div>
          </div>
        </div>

        {/* Help Section */}
        <div className="bg-white rounded-xl shadow-md p-8 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            ¿Necesitas ayuda con tu solicitud?
          </h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Nuestro equipo de atención al ciudadano está disponible para ayudarte 
            con cualquier duda sobre el proceso de solicitud de certificados.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/chat"
              className="inline-flex items-center gap-2 bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
            >
              Hablar con el Asistente IA
            </Link>
            <Link
              href="/contacto"
              className="inline-flex items-center gap-2 bg-white hover:bg-gray-50 text-primary-600 border border-primary-600 px-6 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
            >
              Contactar Soporte
            </Link>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
