'use client';

import React from 'react';
import { useAuth } from '@chia/auth';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { EmailVerification } from '@/components/auth/email-verification';
import { MainNavigation } from '@/components/layout/MainNavigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  User, 
  FileText, 
  MessageSquare, 
  Bell, 
  Settings,
  Calendar,
  CreditCard,
  HelpCircle
} from 'lucide-react';
import Link from 'next/link';

/**
 * Dashboard client component
 * Main dashboard interface for authenticated citizens
 */
export function DashboardClient() {
  const { user, logout } = useAuth();

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <MainNavigation />
        
        <main className="container mx-auto px-4 py-8">
          {/* Email Verification Check */}
          {user && !user.emailConfirmed && (
            <div className="mb-8">
              <EmailVerification />
            </div>
          )}

          {/* Welcome Section */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Bienvenido, {user?.firstName || 'Ciudadano'}
            </h1>
            <p className="text-gray-600">
              Gestiona tus trámites y servicios municipales desde tu panel de control.
            </p>
          </div>

          {/* Quick Actions Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {/* Profile Management */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center space-x-2">
                  <User className="h-5 w-5 text-primary" />
                  <CardTitle className="text-lg">Mi Perfil</CardTitle>
                </div>
                <CardDescription>
                  Actualiza tu información personal
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Link href="/profile">
                  <Button className="w-full">
                    Ver Perfil
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Tramites */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center space-x-2">
                  <FileText className="h-5 w-5 text-primary" />
                  <CardTitle className="text-lg">Mis Trámites</CardTitle>
                </div>
                <CardDescription>
                  Consulta y gestiona tus trámites
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Link href="/tramites">
                  <Button className="w-full">
                    Ver Trámites
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* AI Assistant */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center space-x-2">
                  <MessageSquare className="h-5 w-5 text-primary" />
                  <CardTitle className="text-lg">Asistente IA</CardTitle>
                </div>
                <CardDescription>
                  Obtén ayuda con tus consultas
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Link href="/chat">
                  <Button className="w-full">
                    Iniciar Chat
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Notifications */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center space-x-2">
                  <Bell className="h-5 w-5 text-primary" />
                  <CardTitle className="text-lg">Notificaciones</CardTitle>
                </div>
                <CardDescription>
                  Revisa tus notificaciones
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button className="w-full" variant="outline">
                  Ver Notificaciones
                </Button>
              </CardContent>
            </Card>

            {/* Appointments */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center space-x-2">
                  <Calendar className="h-5 w-5 text-primary" />
                  <CardTitle className="text-lg">Citas</CardTitle>
                </div>
                <CardDescription>
                  Programa y gestiona citas
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button className="w-full" variant="outline">
                  Ver Citas
                </Button>
              </CardContent>
            </Card>

            {/* Payments */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center space-x-2">
                  <CreditCard className="h-5 w-5 text-primary" />
                  <CardTitle className="text-lg">Pagos</CardTitle>
                </div>
                <CardDescription>
                  Realiza pagos en línea
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button className="w-full" variant="outline">
                  Ver Pagos
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Actividad Reciente</CardTitle>
              <CardDescription>
                Tus últimas interacciones con el portal
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                <HelpCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No hay actividad reciente</p>
                <p className="text-sm">Comienza explorando los servicios disponibles</p>
              </div>
            </CardContent>
          </Card>

          {/* Logout Button */}
          <div className="mt-8 text-center">
            <Button onClick={handleLogout} variant="outline">
              Cerrar Sesión
            </Button>
          </div>
        </main>
      </div>
    </ProtectedRoute>
  );
}
