import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import * as speakeasy from 'speakeasy';
import { randomBytes } from 'crypto';

/**
 * POST /api/auth/2fa/verify
 * Verify TOTP code and generate backup codes
 */
export async function POST(request: NextRequest) {
  try {
    const { code, secret } = await request.json();

    if (!code || !secret) {
      return NextResponse.json(
        { error: 'Código y secreto requeridos' },
        { status: 400 }
      );
    }

    const supabase = createRouteHandlerClient({ cookies });

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Usuario no autenticado' },
        { status: 401 }
      );
    }

    // Verify the TOTP code
    const verified = speakeasy.totp.verify({
      secret: secret,
      encoding: 'base32',
      token: code,
      window: 2 // Allow 2 time steps before/after current time
    });

    if (!verified) {
      return NextResponse.json(
        { error: 'Código de verificación inválido' },
        { status: 400 }
      );
    }

    // Generate backup codes
    const backupCodes = Array.from({ length: 8 }, () => {
      return randomBytes(4).toString('hex').toUpperCase();
    });

    // Hash backup codes for storage
    const hashedBackupCodes = await Promise.all(
      backupCodes.map(async (code) => {
        const encoder = new TextEncoder();
        const data = encoder.encode(code);
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        return Array.from(new Uint8Array(hashBuffer))
          .map(b => b.toString(16).padStart(2, '0'))
          .join('');
      })
    );

    return NextResponse.json({
      verified: true,
      backupCodes: backupCodes,
      hashedBackupCodes: hashedBackupCodes
    });

  } catch (error) {
    console.error('Error in 2FA verification:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
