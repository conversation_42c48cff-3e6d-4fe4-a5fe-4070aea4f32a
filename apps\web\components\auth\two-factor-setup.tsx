'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@chia/auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  Shield, 
  Smartphone, 
  Key, 
  Copy, 
  CheckCircle, 
  AlertCircle,
  Loader2,
  QrCode
} from 'lucide-react';

interface TwoFactorSetupProps {
  onComplete?: () => void;
  onCancel?: () => void;
}

/**
 * Two-Factor Authentication setup component
 * Handles TOTP setup, QR code display, and verification
 */
export function TwoFactorSetup({ onComplete, onCancel }: TwoFactorSetupProps) {
  const { user } = useAuth();
  const [step, setStep] = useState<'setup' | 'verify' | 'backup' | 'complete'>('setup');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [secret, setSecret] = useState<string>('');
  const [verificationCode, setVerificationCode] = useState<string>('');
  const [backupCodes, setBackupCodes] = useState<string[]>([]);
  const [copiedSecret, setCopiedSecret] = useState(false);
  const [copiedBackup, setCopiedBackup] = useState(false);

  useEffect(() => {
    if (step === 'setup') {
      generateTwoFactorSecret();
    }
  }, [step]);

  const generateTwoFactorSecret = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/auth/2fa/setup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Error al generar código 2FA');
      }

      const data = await response.json();
      setQrCodeUrl(data.qrCodeUrl);
      setSecret(data.secret);
    } catch (error) {
      console.error('Error generating 2FA secret:', error);
      setError(error instanceof Error ? error.message : 'Error al configurar 2FA');
    } finally {
      setIsLoading(false);
    }
  };

  const verifyTwoFactorCode = async () => {
    if (!verificationCode || verificationCode.length !== 6) {
      setError('Ingresa un código de 6 dígitos');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/auth/2fa/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: verificationCode,
          secret: secret
        }),
      });

      if (!response.ok) {
        throw new Error('Código de verificación inválido');
      }

      const data = await response.json();
      setBackupCodes(data.backupCodes);
      setStep('backup');
    } catch (error) {
      console.error('Error verifying 2FA code:', error);
      setError(error instanceof Error ? error.message : 'Código de verificación inválido');
    } finally {
      setIsLoading(false);
    }
  };

  const completeTwoFactorSetup = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/auth/2fa/enable', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          secret: secret,
          backupCodes: backupCodes
        }),
      });

      if (!response.ok) {
        throw new Error('Error al activar 2FA');
      }

      setStep('complete');
      setTimeout(() => {
        onComplete?.();
      }, 2000);
    } catch (error) {
      console.error('Error enabling 2FA:', error);
      setError(error instanceof Error ? error.message : 'Error al activar 2FA');
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = async (text: string, type: 'secret' | 'backup') => {
    try {
      await navigator.clipboard.writeText(text);
      if (type === 'secret') {
        setCopiedSecret(true);
        setTimeout(() => setCopiedSecret(false), 2000);
      } else {
        setCopiedBackup(true);
        setTimeout(() => setCopiedBackup(false), 2000);
      }
    } catch (error) {
      console.error('Error copying to clipboard:', error);
    }
  };

  const renderSetupStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
          <QrCode className="w-8 h-8 text-blue-600" />
        </div>
        <h3 className="text-lg font-semibold mb-2">Configurar Autenticación de Dos Factores</h3>
        <p className="text-gray-600 text-sm">
          Escanea el código QR con tu aplicación de autenticación favorita
        </p>
      </div>

      {isLoading ? (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : (
        <>
          {qrCodeUrl && (
            <div className="flex justify-center">
              <img src={qrCodeUrl} alt="QR Code para 2FA" className="border rounded-lg" />
            </div>
          )}

          <div className="space-y-2">
            <Label>O ingresa este código manualmente:</Label>
            <div className="flex items-center space-x-2">
              <Input
                value={secret}
                readOnly
                className="font-mono text-sm"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(secret, 'secret')}
              >
                {copiedSecret ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
              </Button>
            </div>
          </div>

          <Alert>
            <Smartphone className="h-4 w-4" />
            <AlertDescription>
              Aplicaciones recomendadas: Google Authenticator, Authy, Microsoft Authenticator
            </AlertDescription>
          </Alert>

          <div className="flex space-x-4">
            <Button onClick={() => setStep('verify')} className="flex-1">
              Continuar
            </Button>
            {onCancel && (
              <Button variant="outline" onClick={onCancel}>
                Cancelar
              </Button>
            )}
          </div>
        </>
      )}
    </div>
  );

  const renderVerifyStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
          <Key className="w-8 h-8 text-green-600" />
        </div>
        <h3 className="text-lg font-semibold mb-2">Verificar Configuración</h3>
        <p className="text-gray-600 text-sm">
          Ingresa el código de 6 dígitos de tu aplicación de autenticación
        </p>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="verificationCode">Código de Verificación</Label>
          <Input
            id="verificationCode"
            type="text"
            placeholder="123456"
            value={verificationCode}
            onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
            className="text-center text-lg font-mono tracking-widest"
            maxLength={6}
          />
        </div>

        <div className="flex space-x-4">
          <Button
            onClick={verifyTwoFactorCode}
            disabled={isLoading || verificationCode.length !== 6}
            className="flex-1"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Verificando...
              </>
            ) : (
              'Verificar Código'
            )}
          </Button>
          <Button variant="outline" onClick={() => setStep('setup')}>
            Volver
          </Button>
        </div>
      </div>
    </div>
  );

  const renderBackupStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <div className="mx-auto w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
          <Shield className="w-8 h-8 text-yellow-600" />
        </div>
        <h3 className="text-lg font-semibold mb-2">Códigos de Respaldo</h3>
        <p className="text-gray-600 text-sm">
          Guarda estos códigos en un lugar seguro. Puedes usarlos si pierdes acceso a tu dispositivo.
        </p>
      </div>

      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          <strong>Importante:</strong> Cada código solo se puede usar una vez. Guárdalos de forma segura.
        </AlertDescription>
      </Alert>

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label>Códigos de Respaldo</Label>
          <Button
            variant="outline"
            size="sm"
            onClick={() => copyToClipboard(backupCodes.join('\n'), 'backup')}
          >
            {copiedBackup ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
            {copiedBackup ? 'Copiado' : 'Copiar'}
          </Button>
        </div>
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="grid grid-cols-2 gap-2 font-mono text-sm">
            {backupCodes.map((code, index) => (
              <div key={index} className="p-2 bg-white rounded border">
                {code}
              </div>
            ))}
          </div>
        </div>
      </div>

      <Button onClick={completeTwoFactorSetup} disabled={isLoading} className="w-full">
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Activando 2FA...
          </>
        ) : (
          'Activar Autenticación de Dos Factores'
        )}
      </Button>
    </div>
  );

  const renderCompleteStep = () => (
    <div className="space-y-6 text-center">
      <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
        <CheckCircle className="w-8 h-8 text-green-600" />
      </div>
      <div>
        <h3 className="text-lg font-semibold mb-2">¡2FA Activado Exitosamente!</h3>
        <p className="text-gray-600 text-sm">
          Tu cuenta ahora está protegida con autenticación de dos factores.
        </p>
      </div>
      <Badge variant="default" className="px-4 py-2">
        <Shield className="w-4 h-4 mr-2" />
        Cuenta Protegida
      </Badge>
    </div>
  );

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Shield className="h-5 w-5" />
          <span>Autenticación de Dos Factores</span>
        </CardTitle>
        <CardDescription>
          Añade una capa extra de seguridad a tu cuenta
        </CardDescription>
      </CardHeader>

      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {step === 'setup' && renderSetupStep()}
        {step === 'verify' && renderVerifyStep()}
        {step === 'backup' && renderBackupStep()}
        {step === 'complete' && renderCompleteStep()}
      </CardContent>
    </Card>
  );
}
