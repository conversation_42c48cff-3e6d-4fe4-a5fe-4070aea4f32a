import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import * as speakeasy from 'speakeasy';

/**
 * POST /api/auth/2fa/authenticate
 * Authenticate user with 2FA code during login
 */
export async function POST(request: NextRequest) {
  try {
    const { email, code, isBackupCode = false } = await request.json();

    if (!email || !code) {
      return NextResponse.json(
        { error: 'Email y código requeridos' },
        { status: 400 }
      );
    }

    const supabase = createRouteHandlerClient({ cookies });

    // Get user by email
    const { data: authUser, error: authError } = await supabase.auth.admin.getUserByEmail(email);
    
    if (authError || !authUser.user) {
      return NextResponse.json(
        { error: 'Usuario no encontrado' },
        { status: 404 }
      );
    }

    // Get 2FA settings
    const { data: twoFactorData, error: twoFactorError } = await supabase
      .from('user_two_factor')
      .select('*')
      .eq('user_id', authUser.user.id)
      .eq('enabled', true)
      .single();

    if (twoFactorError || !twoFactorData) {
      return NextResponse.json(
        { error: '2FA no está configurado para este usuario' },
        { status: 400 }
      );
    }

    let isValid = false;

    if (isBackupCode) {
      // Verify backup code
      const hashedInputCode = await hashCode(code);
      const backupCodeIndex = twoFactorData.backup_codes.findIndex(
        (hashedCode: string) => hashedCode === hashedInputCode
      );

      if (backupCodeIndex !== -1) {
        isValid = true;
        
        // Remove used backup code
        const updatedBackupCodes = [...twoFactorData.backup_codes];
        updatedBackupCodes.splice(backupCodeIndex, 1);
        
        await supabase
          .from('user_two_factor')
          .update({ 
            backup_codes: updatedBackupCodes,
            last_used_at: new Date().toISOString()
          })
          .eq('user_id', authUser.user.id);

        // Log backup code usage
        await supabase
          .from('audit_logs')
          .insert({
            user_id: authUser.user.id,
            action: '2fa_backup_code_used',
            resource_type: 'user',
            resource_id: authUser.user.id,
            details: { backup_codes_remaining: updatedBackupCodes.length },
            created_at: new Date().toISOString()
          });
      }
    } else {
      // Verify TOTP code
      isValid = speakeasy.totp.verify({
        secret: twoFactorData.secret,
        encoding: 'base32',
        token: code,
        window: 2 // Allow 2 time steps before/after current time
      });

      if (isValid) {
        // Update last used timestamp
        await supabase
          .from('user_two_factor')
          .update({ last_used_at: new Date().toISOString() })
          .eq('user_id', authUser.user.id);
      }
    }

    if (!isValid) {
      // Log failed attempt
      await supabase
        .from('audit_logs')
        .insert({
          user_id: authUser.user.id,
          action: '2fa_failed_attempt',
          resource_type: 'user',
          resource_id: authUser.user.id,
          details: { 
            type: isBackupCode ? 'backup_code' : 'totp',
            timestamp: new Date().toISOString()
          },
          created_at: new Date().toISOString()
        });

      return NextResponse.json(
        { error: 'Código de verificación inválido' },
        { status: 400 }
      );
    }

    // Generate session token or return success
    // In a real implementation, you might want to generate a temporary token
    // that can be used to complete the login process
    const sessionToken = generateSessionToken(authUser.user.id);

    // Log successful 2FA
    await supabase
      .from('audit_logs')
      .insert({
        user_id: authUser.user.id,
        action: '2fa_success',
        resource_type: 'user',
        resource_id: authUser.user.id,
        details: { 
          type: isBackupCode ? 'backup_code' : 'totp',
          timestamp: new Date().toISOString()
        },
        created_at: new Date().toISOString()
      });

    return NextResponse.json({
      success: true,
      token: sessionToken,
      message: '2FA verificado exitosamente'
    });

  } catch (error) {
    console.error('Error in 2FA authentication:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

/**
 * Hash a code using SHA-256
 */
async function hashCode(code: string): Promise<string> {
  const encoder = new TextEncoder();
  const data = encoder.encode(code);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  return Array.from(new Uint8Array(hashBuffer))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}

/**
 * Generate a temporary session token for 2FA completion
 * In production, this should be a proper JWT or similar secure token
 */
function generateSessionToken(userId: string): string {
  const timestamp = Date.now();
  const randomBytes = crypto.getRandomValues(new Uint8Array(16));
  const randomString = Array.from(randomBytes, byte => byte.toString(16).padStart(2, '0')).join('');
  
  // This is a simple implementation - in production use proper JWT
  return `2fa_${userId}_${timestamp}_${randomString}`;
}
