/**
 * Database types for CHIA Portal Ciudadano Digital
 * Generated from Supabase schema
 */

export interface Database {
  public: {
    Tables: {
      ciudadano: {
        Row: {
          id: string;
          email: string;
          first_name: string;
          last_name: string;
          document_type: 'CC' | 'CE' | 'TI' | 'PP';
          document_number: string;
          phone: string | null;
          address: string | null;
          city: string;
          department: string;
          birth_date: string | null;
          is_email_verified: boolean;
          is_phone_verified: boolean;
          two_factor_enabled: boolean;
          two_factor_secret: string | null;
          backup_codes: string[] | null;
          last_login_at: string | null;
          created_at: string;
          updated_at: string;
          metadata: Record<string, any> | null;
        };
        Insert: {
          id?: string;
          email: string;
          first_name: string;
          last_name: string;
          document_type: 'CC' | 'CE' | 'TI' | 'PP';
          document_number: string;
          phone?: string | null;
          address?: string | null;
          city: string;
          department: string;
          birth_date?: string | null;
          is_email_verified?: boolean;
          is_phone_verified?: boolean;
          two_factor_enabled?: boolean;
          two_factor_secret?: string | null;
          backup_codes?: string[] | null;
          last_login_at?: string | null;
          created_at?: string;
          updated_at?: string;
          metadata?: Record<string, any> | null;
        };
        Update: {
          id?: string;
          email?: string;
          first_name?: string;
          last_name?: string;
          document_type?: 'CC' | 'CE' | 'TI' | 'PP';
          document_number?: string;
          phone?: string | null;
          address?: string | null;
          city?: string;
          department?: string;
          birth_date?: string | null;
          is_email_verified?: boolean;
          is_phone_verified?: boolean;
          two_factor_enabled?: boolean;
          two_factor_secret?: string | null;
          backup_codes?: string[] | null;
          last_login_at?: string | null;
          created_at?: string;
          updated_at?: string;
          metadata?: Record<string, any> | null;
        };
      };
      admin_user: {
        Row: {
          id: string;
          user_id: string;
          role: 'admin_municipal' | 'admin_sistema' | 'operador';
          permissions: string[];
          municipality_id: string | null;
          department_id: string | null;
          is_active: boolean;
          created_by: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          role: 'admin_municipal' | 'admin_sistema' | 'operador';
          permissions?: string[];
          municipality_id?: string | null;
          department_id?: string | null;
          is_active?: boolean;
          created_by: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          role?: 'admin_municipal' | 'admin_sistema' | 'operador';
          permissions?: string[];
          municipality_id?: string | null;
          department_id?: string | null;
          is_active?: boolean;
          created_by?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      user_sessions: {
        Row: {
          id: string;
          user_id: string;
          session_token: string;
          refresh_token: string;
          expires_at: string;
          ip_address: string | null;
          user_agent: string | null;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          session_token: string;
          refresh_token: string;
          expires_at: string;
          ip_address?: string | null;
          user_agent?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          session_token?: string;
          refresh_token?: string;
          expires_at?: string;
          ip_address?: string | null;
          user_agent?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      audit_log: {
        Row: {
          id: string;
          user_id: string | null;
          action: string;
          resource_type: string;
          resource_id: string | null;
          details: Record<string, any> | null;
          ip_address: string | null;
          user_agent: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id?: string | null;
          action: string;
          resource_type: string;
          resource_id?: string | null;
          details?: Record<string, any> | null;
          ip_address?: string | null;
          user_agent?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string | null;
          action?: string;
          resource_type?: string;
          resource_id?: string | null;
          details?: Record<string, any> | null;
          ip_address?: string | null;
          user_agent?: string | null;
          created_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      user_role: 'ciudadano' | 'admin_municipal' | 'admin_sistema' | 'operador';
      document_type: 'CC' | 'CE' | 'TI' | 'PP';
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
}
