'use client';

import React, { useEffect, useState } from 'react';
import { useAuth } from '@chia/auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Clock, 
  Shield, 
  Monitor, 
  Smartphone, 
  AlertTriangle,
  RefreshCw,
  LogOut,
  Eye,
  EyeOff
} from 'lucide-react';

interface SessionInfo {
  id: string;
  deviceType: string;
  browser: string;
  location: string;
  lastActive: string;
  current: boolean;
  ipAddress: string;
}

interface SessionManagerProps {
  onSessionRevoked?: (sessionId: string) => void;
}

/**
 * Session Manager component
 * Displays and manages user sessions across devices
 */
export function SessionManager({ onSessionRevoked }: SessionManagerProps) {
  const { user, refreshSession, logout } = useAuth();
  const [sessions, setSessions] = useState<SessionInfo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showDetails, setShowDetails] = useState<string | null>(null);

  useEffect(() => {
    fetchActiveSessions();
  }, []);

  const fetchActiveSessions = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/auth/sessions', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Error al obtener sesiones activas');
      }

      const data = await response.json();
      setSessions(data.sessions || []);
    } catch (error) {
      console.error('Error fetching sessions:', error);
      setError(error instanceof Error ? error.message : 'Error al cargar sesiones');
    } finally {
      setIsLoading(false);
    }
  };

  const revokeSession = async (sessionId: string) => {
    try {
      const response = await fetch('/api/auth/sessions/revoke', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sessionId }),
      });

      if (!response.ok) {
        throw new Error('Error al revocar sesión');
      }

      // Remove session from list
      setSessions(prev => prev.filter(session => session.id !== sessionId));
      onSessionRevoked?.(sessionId);

      // If current session was revoked, logout
      const revokedSession = sessions.find(s => s.id === sessionId);
      if (revokedSession?.current) {
        await logout();
      }
    } catch (error) {
      console.error('Error revoking session:', error);
      setError(error instanceof Error ? error.message : 'Error al revocar sesión');
    }
  };

  const revokeAllOtherSessions = async () => {
    try {
      const response = await fetch('/api/auth/sessions/revoke-all', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Error al revocar sesiones');
      }

      // Keep only current session
      setSessions(prev => prev.filter(session => session.current));
    } catch (error) {
      console.error('Error revoking all sessions:', error);
      setError(error instanceof Error ? error.message : 'Error al revocar sesiones');
    }
  };

  const refreshCurrentSession = async () => {
    try {
      await refreshSession();
      await fetchActiveSessions();
    } catch (error) {
      console.error('Error refreshing session:', error);
      setError('Error al actualizar sesión');
    }
  };

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType.toLowerCase()) {
      case 'mobile':
        return <Smartphone className="h-4 w-4" />;
      case 'tablet':
        return <Monitor className="h-4 w-4" />;
      default:
        return <Monitor className="h-4 w-4" />;
    }
  };

  const formatLastActive = (lastActive: string) => {
    const date = new Date(lastActive);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'Ahora mismo';
    if (diffMins < 60) return `Hace ${diffMins} minutos`;
    if (diffHours < 24) return `Hace ${diffHours} horas`;
    return `Hace ${diffDays} días`;
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-6 w-6 animate-spin text-primary" />
            <span className="ml-2">Cargando sesiones...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5" />
              <span>Gestión de Sesiones</span>
            </CardTitle>
            <CardDescription>
              Administra tus sesiones activas en diferentes dispositivos
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" onClick={fetchActiveSessions}>
              <RefreshCw className="h-4 w-4 mr-1" />
              Actualizar
            </Button>
            {sessions.filter(s => !s.current).length > 0 && (
              <Button variant="destructive" size="sm" onClick={revokeAllOtherSessions}>
                <LogOut className="h-4 w-4 mr-1" />
                Cerrar Otras
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {sessions.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Shield className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No hay sesiones activas</p>
          </div>
        ) : (
          <div className="space-y-3">
            {sessions.map((session) => (
              <div
                key={session.id}
                className={`p-4 border rounded-lg ${
                  session.current ? 'border-green-200 bg-green-50' : 'border-gray-200'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getDeviceIcon(session.deviceType)}
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">{session.browser}</span>
                        {session.current && (
                          <Badge variant="default" className="text-xs">
                            Sesión Actual
                          </Badge>
                        )}
                      </div>
                      <div className="text-sm text-gray-500">
                        {session.location} • {formatLastActive(session.lastActive)}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowDetails(
                        showDetails === session.id ? null : session.id
                      )}
                    >
                      {showDetails === session.id ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>

                    {!session.current && (
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => revokeSession(session.id)}
                      >
                        <LogOut className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>

                {showDetails === session.id && (
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium">ID de Sesión:</span>
                        <p className="text-gray-600 font-mono text-xs">
                          {session.id.substring(0, 16)}...
                        </p>
                      </div>
                      <div>
                        <span className="font-medium">Dirección IP:</span>
                        <p className="text-gray-600">{session.ipAddress}</p>
                      </div>
                      <div>
                        <span className="font-medium">Tipo de Dispositivo:</span>
                        <p className="text-gray-600">{session.deviceType}</p>
                      </div>
                      <div>
                        <span className="font-medium">Última Actividad:</span>
                        <p className="text-gray-600">
                          {new Date(session.lastActive).toLocaleString('es-CO')}
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        <Alert>
          <Clock className="h-4 w-4" />
          <AlertDescription>
            <strong>Consejo de seguridad:</strong> Revisa regularmente tus sesiones activas y 
            cierra aquellas que no reconozcas. Las sesiones se renuevan automáticamente cada 24 horas.
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
}
