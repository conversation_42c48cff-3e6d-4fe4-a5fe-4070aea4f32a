/**
 * Validador de datos para el sistema de ingesta CHIA
 * Arquitecto: Winston 🏗️
 * Fecha: 2025-01-07
 */

import {
  ValidationRule,
  ValidationResult,
  ValidationError,
  Dependencia,
  Subdependencia,
  FAQ,
  Tramite,
  OPA,
  FAQSourceData,
  TramiteSourceData,
  OPASourceData
} from './types';

// =====================================================
// VALIDADOR BASE
// =====================================================

export class DataValidator {
  private rules: Map<string, ValidationRule[]> = new Map();

  constructor() {
    this.initializeRules();
  }

  /**
   * Inicializa las reglas de validación para cada tipo de dato
   */
  private initializeRules(): void {
    // Reglas para dependencias
    this.rules.set('dependencia', [
      { field: 'codigo', type: 'required' },
      { field: 'codigo', type: 'string', minLength: 3, maxLength: 3, pattern: /^\d{3}$/ },
      { field: 'nombre', type: 'required' },
      { field: 'nombre', type: 'string', minLength: 1, maxLength: 500 },
      { field: 'sigla', type: 'string', maxLength: 10 }
    ]);

    // Reglas para subdependencias
    this.rules.set('subdependencia', [
      { field: 'codigo', type: 'required' },
      { field: 'codigo', type: 'string', minLength: 3, maxLength: 3, pattern: /^\d{3}$/ },
      { field: 'nombre', type: 'required' },
      { field: 'nombre', type: 'string', minLength: 1, maxLength: 500 },
      { field: 'dependencia_id', type: 'required' },
      { field: 'dependencia_id', type: 'string' }
    ]);

    // Reglas para FAQs
    this.rules.set('faq', [
      { field: 'dependencia_id', type: 'required' },
      { field: 'dependencia_id', type: 'string' },
      { field: 'tema', type: 'required' },
      { field: 'tema', type: 'string', minLength: 1, maxLength: 1000 },
      { field: 'pregunta', type: 'required' },
      { field: 'pregunta', type: 'string', minLength: 1, maxLength: 2000 },
      { field: 'respuesta', type: 'required' },
      { field: 'respuesta', type: 'string', minLength: 1, maxLength: 10000 },
      { field: 'palabras_clave', type: 'array' },
      { 
        field: 'prioridad', 
        type: 'number',
        custom: (value: any) => {
          const num = Number(value);
          return (num >= 0 && num <= 10) || 'La prioridad debe estar entre 0 y 10';
        }
      }
    ]);

    // Reglas para trámites
    this.rules.set('tramite', [
      { field: 'nombre', type: 'required' },
      { field: 'nombre', type: 'string', minLength: 1, maxLength: 1000 },
      { field: 'dependencia_id', type: 'required' },
      { field: 'dependencia_id', type: 'string' },
      { 
        field: 'url_suit', 
        type: 'url',
        custom: (value: any) => {
          if (!value) return true;
          return /^https?:\/\/.+/.test(value) || 'URL del SUIT debe ser válida';
        }
      },
      { 
        field: 'url_govco', 
        type: 'url',
        custom: (value: any) => {
          if (!value) return true;
          return /^https?:\/\/.+/.test(value) || 'URL de GOV.CO debe ser válida';
        }
      }
    ]);

    // Reglas para OPAs
    this.rules.set('opa', [
      { field: 'codigo_opa', type: 'required' },
      { field: 'codigo_opa', type: 'string', minLength: 1, maxLength: 10 },
      { field: 'descripcion', type: 'required' },
      { field: 'descripcion', type: 'string', minLength: 1, maxLength: 5000 },
      { field: 'dependencia_id', type: 'required' },
      { field: 'dependencia_id', type: 'string' },
      { 
        field: 'nivel_complejidad', 
        type: 'number',
        custom: (value: any) => {
          if (value === undefined || value === null) return true;
          const num = Number(value);
          return (num >= 1 && num <= 5) || 'El nivel de complejidad debe estar entre 1 y 5';
        }
      }
    ]);
  }

  /**
   * Valida un objeto según las reglas definidas
   */
  validate(data: any, type: string): ValidationResult {
    const rules = this.rules.get(type);
    if (!rules) {
      return {
        valid: false,
        errors: [{ field: 'type', message: `Tipo de validación desconocido: ${type}` }]
      };
    }

    const errors: ValidationError[] = [];

    for (const rule of rules) {
      const value = this.getNestedValue(data, rule.field);
      const error = this.validateField(value, rule, rule.field);
      if (error) {
        errors.push(error);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Valida un campo individual según una regla
   */
  private validateField(value: any, rule: ValidationRule, fieldPath: string): ValidationError | null {
    // Validación de campo requerido
    if (rule.type === 'required') {
      if (value === undefined || value === null || value === '') {
        return {
          field: fieldPath,
          message: `El campo ${fieldPath} es requerido`,
          value
        };
      }
      return null;
    }

    // Si el valor está vacío y no es requerido, no validar más
    if (value === undefined || value === null || value === '') {
      return null;
    }

    // Validación por tipo
    switch (rule.type) {
      case 'string':
        if (typeof value !== 'string') {
          return {
            field: fieldPath,
            message: `El campo ${fieldPath} debe ser una cadena de texto`,
            value
          };
        }
        
        if (rule.minLength && value.length < rule.minLength) {
          return {
            field: fieldPath,
            message: `El campo ${fieldPath} debe tener al menos ${rule.minLength} caracteres`,
            value
          };
        }
        
        if (rule.maxLength && value.length > rule.maxLength) {
          return {
            field: fieldPath,
            message: `El campo ${fieldPath} no puede tener más de ${rule.maxLength} caracteres`,
            value
          };
        }
        
        if (rule.pattern && !rule.pattern.test(value)) {
          return {
            field: fieldPath,
            message: `El campo ${fieldPath} no cumple con el formato requerido`,
            value
          };
        }
        break;

      case 'number':
        if (typeof value !== 'number' && isNaN(Number(value))) {
          return {
            field: fieldPath,
            message: `El campo ${fieldPath} debe ser un número`,
            value
          };
        }
        break;

      case 'array':
        if (!Array.isArray(value)) {
          return {
            field: fieldPath,
            message: `El campo ${fieldPath} debe ser un arreglo`,
            value
          };
        }
        break;

      case 'url':
        if (typeof value === 'string' && value.length > 0) {
          try {
            new URL(value);
          } catch {
            return {
              field: fieldPath,
              message: `El campo ${fieldPath} debe ser una URL válida`,
              value
            };
          }
        }
        break;
    }

    // Validación personalizada
    if (rule.custom) {
      const customResult = rule.custom(value);
      if (customResult !== true) {
        return {
          field: fieldPath,
          message: typeof customResult === 'string' ? customResult : `Validación personalizada falló para ${fieldPath}`,
          value
        };
      }
    }

    return null;
  }

  /**
   * Obtiene un valor anidado de un objeto usando notación de punto
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * Valida la estructura de un archivo JSON de FAQs
   */
  validateFAQFile(data: any): ValidationResult {
    if (!data || !data.faqs || !Array.isArray(data.faqs)) {
      return {
        valid: false,
        errors: [{ field: 'faqs', message: 'El archivo debe contener un arreglo "faqs"' }]
      };
    }

    const errors: ValidationError[] = [];

    data.faqs.forEach((faqGroup: any, index: number) => {
      if (!faqGroup.dependencia || !faqGroup.codigo_dependencia) {
        errors.push({
          field: `faqs[${index}]`,
          message: 'Cada grupo de FAQs debe tener dependencia y codigo_dependencia'
        });
      }

      if (!faqGroup.temas || !Array.isArray(faqGroup.temas)) {
        errors.push({
          field: `faqs[${index}].temas`,
          message: 'Cada grupo de FAQs debe tener un arreglo de temas'
        });
      } else {
        faqGroup.temas.forEach((tema: any, temaIndex: number) => {
          if (!tema.preguntas_frecuentes || !Array.isArray(tema.preguntas_frecuentes)) {
            errors.push({
              field: `faqs[${index}].temas[${temaIndex}].preguntas_frecuentes`,
              message: 'Cada tema debe tener un arreglo de preguntas_frecuentes'
            });
          }
        });
      }
    });

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Valida la estructura de un archivo JSON de trámites
   */
  validateTramiteFile(data: any): ValidationResult {
    if (!Array.isArray(data)) {
      return {
        valid: false,
        errors: [{ field: 'root', message: 'El archivo de trámites debe ser un arreglo' }]
      };
    }

    const errors: ValidationError[] = [];

    data.forEach((tramite: any, index: number) => {
      if (!tramite.Nombre) {
        errors.push({
          field: `[${index}].Nombre`,
          message: 'Cada trámite debe tener un nombre'
        });
      }

      if (!tramite.dependencia || !tramite.codigo_dependencia) {
        errors.push({
          field: `[${index}]`,
          message: 'Cada trámite debe tener dependencia y codigo_dependencia'
        });
      }
    });

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Valida la estructura de un archivo JSON de OPAs
   */
  validateOPAFile(data: any): ValidationResult {
    if (!data || !data.dependencias || typeof data.dependencias !== 'object') {
      return {
        valid: false,
        errors: [{ field: 'dependencias', message: 'El archivo debe contener un objeto "dependencias"' }]
      };
    }

    const errors: ValidationError[] = [];

    Object.entries(data.dependencias).forEach(([codigoDep, dependencia]: [string, any]) => {
      if (!dependencia.nombre) {
        errors.push({
          field: `dependencias.${codigoDep}.nombre`,
          message: 'Cada dependencia debe tener un nombre'
        });
      }

      if (dependencia.subdependencias && typeof dependencia.subdependencias === 'object') {
        Object.entries(dependencia.subdependencias).forEach(([codigoSub, subdependencia]: [string, any]) => {
          if (!subdependencia.OPA || !Array.isArray(subdependencia.OPA)) {
            errors.push({
              field: `dependencias.${codigoDep}.subdependencias.${codigoSub}.OPA`,
              message: 'Cada subdependencia debe tener un arreglo de OPA'
            });
          }
        });
      }
    });

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Valida que los códigos de dependencia sean únicos
   */
  validateUniqueCodes(dependencias: Dependencia[]): ValidationResult {
    const codes = new Set<string>();
    const errors: ValidationError[] = [];

    dependencias.forEach((dep, index) => {
      if (codes.has(dep.codigo)) {
        errors.push({
          field: `dependencias[${index}].codigo`,
          message: `Código de dependencia duplicado: ${dep.codigo}`
        });
      } else {
        codes.add(dep.codigo);
      }
    });

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Valida integridad referencial entre dependencias y subdependencias
   */
  validateReferentialIntegrity(
    dependencias: Dependencia[],
    subdependencias: Subdependencia[]
  ): ValidationResult {
    const depIds = new Set(dependencias.map(d => d.id).filter(Boolean));
    const errors: ValidationError[] = [];

    subdependencias.forEach((sub, index) => {
      if (!depIds.has(sub.dependencia_id)) {
        errors.push({
          field: `subdependencias[${index}].dependencia_id`,
          message: `Referencia a dependencia inexistente: ${sub.dependencia_id}`
        });
      }
    });

    return {
      valid: errors.length === 0,
      errors
    };
  }
}
